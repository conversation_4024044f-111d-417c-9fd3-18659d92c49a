window=global
navigator={}
var  enc;
!function(e) {
    function t(t) {
        for (var n, r, i = t[0], a = t[1], u = 0, c = []; u < i.length; u++)
            r = i[u],
            Object.prototype.hasOwnProperty.call(o, r) && o[r] && c.push(o[r][0]),
            o[r] = 0;
        for (n in a)
            Object.prototype.hasOwnProperty.call(a, n) && (e[n] = a[n]);
        for (s && s(t); c.length; )
            c.shift()()
    }
    var n = {}
      , r = {
        94: 0
    }
      , o = {
        94: 0
    };
    function i(t) {
        if (n[t])
            return n[t].exports;
        var r = n[t] = {
            i: t,
            l: !1,
            exports: {}
        };
        console.log("t",t)
        return e[t].call(r.exports, r, r.exports, i),
        r.l = !0,
        r.exports
    }
    i.e = function(e) {
        var t = [];
        r[e] ? t.push(r[e]) : 0 !== r[e] && {
            1: 1,
            2: 1,
            7: 1,
            8: 1,
            9: 1,
            10: 1,
            11: 1,
            12: 1,
            13: 1,
            14: 1,
            15: 1,
            16: 1,
            17: 1,
            19: 1,
            20: 1,
            21: 1,
            22: 1,
            27: 1,
            28: 1,
            29: 1,
            30: 1,
            31: 1,
            32: 1,
            33: 1,
            34: 1,
            35: 1,
            36: 1,
            37: 1,
            38: 1,
            39: 1,
            40: 1,
            41: 1,
            42: 1,
            43: 1,
            44: 1,
            45: 1,
            46: 1,
            47: 1,
            48: 1,
            50: 1,
            51: 1,
            52: 1,
            53: 1,
            54: 1,
            55: 1,
            56: 1,
            57: 1,
            58: 1,
            59: 1,
            60: 1,
            61: 1,
            62: 1,
            63: 1,
            64: 1,
            65: 1,
            66: 1,
            67: 1,
            68: 1,
            69: 1,
            70: 1,
            72: 1,
            74: 1,
            75: 1,
            76: 1,
            77: 1,
            78: 1,
            79: 1,
            80: 1,
            81: 1,
            82: 1,
            83: 1,
            84: 1,
            85: 1,
            86: 1,
            87: 1,
            88: 1,
            89: 1,
            90: 1,
            91: 1,
            92: 1,
            93: 1,
            96: 1
        }[e] && t.push(r[e] = new Promise((function(t, n) {
            for (var o = "static/css/" + ({
                0: "vendors~PageAccountSetting~PageAdRevenue~PageAnnouncement~PageAudio~PageAuditResult~PageBind~PageCla~e5efe68a",
                1: "PageAccountSetting~PageAdRevenue~PageAnnouncement~PageAudio~PageAuditResult~PageBind~PageCommentList~c43a7b98",
                2: "PageAccountSetting~PageAdRevenue~PageAudio~PageCommentList~PageDetail~PageDetailAdmaster~PageDetailC~b8c1250c",
                3: "vendors~PageClasses~PageImageText~PageMaterialAll~PagePicture~PagePub~PagePubAdEditor~PagePubAlbum~P~bf5c4b4c",
                4: "vendors~PageClasses~PageContent~PageImageText~PageMaterialAll~PageMyPub~PagePub~PagePubAdEditor~Page~b9e37e10",
                5: "vendors~PageClasses~PageDetail~PageImageText~PageLogin~PageMaterialAll~PagePub~PagePubAdEditor~PageP~063ba71f",
                6: "vendors~PageContent~PageMyPub~PageStatic~PageStaticDetail~PageStaticFans~PageVideo~PageVideoDetail",
                7: "vendors~PageContent~PageStatic~PageStaticDetail~PageStaticFans~PageVideo~PageVideoDetail",
                8: "PageDetailAdmaster~PageDetailContent~PageDetailGoods~PageDetailIncom~PageRevenueStats",
                9: "PageAuditResult~PageInfoReq1~PageInfoReq2~PageUserType",
                10: "PageInfoReq1~PageInfoReq2~PageInfoReq2ForSell~PageInfoReqForSell",
                11: "PageBind~PageRegister",
                12: "PagePubAdEditor",
                13: "PageAccountSetting",
                14: "PageAdRevenue",
                15: "PageAnnouncement",
                16: "PageAudio",
                17: "PageAuditResult",
                18: "PageBind",
                19: "PageClasses",
                20: "PageCommentList",
                21: "PageContent",
                22: "PageDetail",
                23: "PageDetailAdmaster",
                24: "PageDetailContent",
                25: "PageDetailGoods",
                26: "PageDetailIncom",
                27: "PageDraft",
                28: "PageEarning",
                29: "PageEncourage",
                30: "PageEvaluate",
                31: "PageEventSquare",
                32: "PageEventSquareDetail",
                33: "PageFansManagement",
                34: "PageFluxMain",
                35: "PageFluxMainAgreement",
                36: "PageHelp",
                37: "PageHome",
                38: "PageImageText",
                39: "PageIndex",
                40: "PageInfoReq1",
                41: "PageInfoReq2",
                42: "PageInfoReq2ForSell",
                43: "PageInfoReqForSell",
                44: "PageInfoReqForSellAuditStatus1",
                45: "PageInfoReqForSellAuditStatus2",
                46: "PageLogin",
                47: "PageMaterialAll",
                48: "PageMaterialVideo",
                49: "PageMedical",
                50: "PageMyPub",
                51: "PageOperaStandard",
                52: "PageOrderManagement",
                53: "PagePaymentContent",
                54: "PagePicture",
                55: "PagePregHomePage",
                56: "PagePub",
                57: "PagePubAlbum",
                58: "PagePubAlbumAggregatio",
                59: "PagePubAlbumEncy",
                60: "PagePubAudio",
                61: "PagePubAudioXMLY",
                62: "PagePubBabySong",
                63: "PagePubBaoXiModel",
                64: "PagePubDietIntegrate",
                65: "PagePubHuiBenAggregatio",
                66: "PagePubNinePic",
                67: "PagePubPicBook",
                68: "PagePubPosts",
                69: "PagePubStory",
                70: "PagePublishStandard",
                71: "PageRegister",
                72: "PageRetrievePassword",
                73: "PageRevenueStats",
                74: "PageRuleIncome",
                75: "PageServiceAgreement",
                76: "PageSettleCenter",
                77: "PageStatic",
                78: "PageStaticDetail",
                79: "PageStaticFans",
                80: "PageSubsidy",
                81: "PageSystemInfomationNoticeItem",
                82: "PageSystemInfomationPrivateMsg",
                83: "PageSystemInfomationSystemMsg",
                84: "PageUploadAudio",
                85: "PageUploadVideo",
                86: "PageUserType",
                87: "PageVideo",
                88: "PageVideoDetail",
                89: "PubAlbumAliloXMLY",
                90: "PubAlbumNetease",
                91: "PubAlbumYiLa",
                92: "PubAudioAliloXMLY",
                93: "PubAudioNetease",
                95: "china",
                96: "vendors~PageMyPub",
                97: "vendors~g2"
            }[e] || e) + "." + {
                0: "31d6cfe0",
                1: "97c2bb28",
                2: "a5e37a82",
                3: "31d6cfe0",
                4: "31d6cfe0",
                5: "31d6cfe0",
                6: "31d6cfe0",
                7: "968b0833",
                8: "645acfc0",
                9: "a603144b",
                10: "591810a5",
                11: "6cb59c95",
                12: "9c41c8d4",
                13: "cdf48e39",
                14: "bbe41527",
                15: "937cfbbe",
                16: "855e89db",
                17: "51113c85",
                18: "31d6cfe0",
                19: "356460d3",
                20: "6246f2c8",
                21: "135eb5ca",
                22: "03eabb9e",
                23: "31d6cfe0",
                24: "31d6cfe0",
                25: "31d6cfe0",
                26: "31d6cfe0",
                27: "a5c5ec83",
                28: "3abd33b5",
                29: "fbbed985",
                30: "10cfbd6b",
                31: "ca6db01c",
                32: "19eea492",
                33: "022fa52b",
                34: "5d8bce9d",
                35: "e8c43ef6",
                36: "9187a489",
                37: "ceba3931",
                38: "138904a1",
                39: "1c46c0fc",
                40: "fe5ab8e7",
                41: "f7977f03",
                42: "0f739ba2",
                43: "da08e4c4",
                44: "e2cd77db",
                45: "3c81d1c8",
                46: "e2306b1d",
                47: "d4ba8630",
                48: "2612e1d9",
                49: "31d6cfe0",
                50: "064ccc66",
                51: "22ecae57",
                52: "7e170e0c",
                53: "2a4ae1b6",
                54: "8dc3758d",
                55: "05e3a478",
                56: "b062bf51",
                57: "16f3cc6e",
                58: "6627b96d",
                59: "184b1edb",
                60: "4fa2da48",
                61: "9a8224a9",
                62: "d47f2d9f",
                63: "911c66fb",
                64: "09eec38b",
                65: "ee30ad63",
                66: "be309047",
                67: "b22c1f20",
                68: "506d0a5e",
                69: "0fc9098d",
                70: "58a520e5",
                71: "31d6cfe0",
                72: "3045c722",
                73: "31d6cfe0",
                74: "b23ca277",
                75: "2d24f522",
                76: "d87c19ba",
                77: "a412c4d2",
                78: "a412c4d2",
                79: "92660d0a",
                80: "81c27e48",
                81: "d16cac4f",
                82: "9ee2be71",
                83: "d16cac4f",
                84: "0153e447",
                85: "4463c21c",
                86: "0f7ff157",
                87: "905acbcb",
                88: "905acbcb",
                89: "16f3cc6e",
                90: "73fdc435",
                91: "73fdc435",
                92: "756d7d3d",
                93: "5145858d",
                95: "31d6cfe0",
                96: "d1d4183c",
                97: "31d6cfe0"
            }[e] + ".chunk.css", a = i.p + o, u = document.getElementsByTagName("link"), c = 0; c < u.length; c++) {
                var s = (f = u[c]).getAttribute("data-href") || f.getAttribute("href");
                if ("stylesheet" === f.rel && (s === o || s === a))
                    return t()
            }
            var l = document.getElementsByTagName("style");
            for (c = 0; c < l.length; c++) {
                var f;
                if ((s = (f = l[c]).getAttribute("data-href")) === o || s === a)
                    return t()
            }
            var p = document.createElement("link");
            p.rel = "stylesheet",
            p.type = "text/css",
            p.onload = t,
            p.onerror = function(t) {
                var o = t && t.target && t.target.src || a
                  , i = new Error("Loading CSS chunk " + e + " failed.\n(" + o + ")");
                i.code = "CSS_CHUNK_LOAD_FAILED",
                i.request = o,
                delete r[e],
                p.parentNode.removeChild(p),
                n(i)
            }
            ,
            p.href = a,
            0 !== p.href.indexOf(window.location.origin + "/") && (p.crossOrigin = "anonymous"),
            document.getElementsByTagName("head")[0].appendChild(p)
        }
        )).then((function() {
            r[e] = 0
        }
        )));
        var n = o[e];
        if (0 !== n)
            if (n)
                t.push(n[2]);
            else {
                var a = new Promise((function(t, r) {
                    n = o[e] = [t, r]
                }
                ));
                t.push(n[2] = a);
                var u, c = document.createElement("script");
                c.charset = "utf-8",
                c.timeout = 120,
                i.nc && c.setAttribute("nonce", i.nc),
                c.src = function(e) {
                    return i.p + "static/js/" + ({
                        0: "vendors~PageAccountSetting~PageAdRevenue~PageAnnouncement~PageAudio~PageAuditResult~PageBind~PageCla~e5efe68a",
                        1: "PageAccountSetting~PageAdRevenue~PageAnnouncement~PageAudio~PageAuditResult~PageBind~PageCommentList~c43a7b98",
                        2: "PageAccountSetting~PageAdRevenue~PageAudio~PageCommentList~PageDetail~PageDetailAdmaster~PageDetailC~b8c1250c",
                        3: "vendors~PageClasses~PageImageText~PageMaterialAll~PagePicture~PagePub~PagePubAdEditor~PagePubAlbum~P~bf5c4b4c",
                        4: "vendors~PageClasses~PageContent~PageImageText~PageMaterialAll~PageMyPub~PagePub~PagePubAdEditor~Page~b9e37e10",
                        5: "vendors~PageClasses~PageDetail~PageImageText~PageLogin~PageMaterialAll~PagePub~PagePubAdEditor~PageP~063ba71f",
                        6: "vendors~PageContent~PageMyPub~PageStatic~PageStaticDetail~PageStaticFans~PageVideo~PageVideoDetail",
                        7: "vendors~PageContent~PageStatic~PageStaticDetail~PageStaticFans~PageVideo~PageVideoDetail",
                        8: "PageDetailAdmaster~PageDetailContent~PageDetailGoods~PageDetailIncom~PageRevenueStats",
                        9: "PageAuditResult~PageInfoReq1~PageInfoReq2~PageUserType",
                        10: "PageInfoReq1~PageInfoReq2~PageInfoReq2ForSell~PageInfoReqForSell",
                        11: "PageBind~PageRegister",
                        12: "PagePubAdEditor",
                        13: "PageAccountSetting",
                        14: "PageAdRevenue",
                        15: "PageAnnouncement",
                        16: "PageAudio",
                        17: "PageAuditResult",
                        18: "PageBind",
                        19: "PageClasses",
                        20: "PageCommentList",
                        21: "PageContent",
                        22: "PageDetail",
                        23: "PageDetailAdmaster",
                        24: "PageDetailContent",
                        25: "PageDetailGoods",
                        26: "PageDetailIncom",
                        27: "PageDraft",
                        28: "PageEarning",
                        29: "PageEncourage",
                        30: "PageEvaluate",
                        31: "PageEventSquare",
                        32: "PageEventSquareDetail",
                        33: "PageFansManagement",
                        34: "PageFluxMain",
                        35: "PageFluxMainAgreement",
                        36: "PageHelp",
                        37: "PageHome",
                        38: "PageImageText",
                        39: "PageIndex",
                        40: "PageInfoReq1",
                        41: "PageInfoReq2",
                        42: "PageInfoReq2ForSell",
                        43: "PageInfoReqForSell",
                        44: "PageInfoReqForSellAuditStatus1",
                        45: "PageInfoReqForSellAuditStatus2",
                        46: "PageLogin",
                        47: "PageMaterialAll",
                        48: "PageMaterialVideo",
                        49: "PageMedical",
                        50: "PageMyPub",
                        51: "PageOperaStandard",
                        52: "PageOrderManagement",
                        53: "PagePaymentContent",
                        54: "PagePicture",
                        55: "PagePregHomePage",
                        56: "PagePub",
                        57: "PagePubAlbum",
                        58: "PagePubAlbumAggregatio",
                        59: "PagePubAlbumEncy",
                        60: "PagePubAudio",
                        61: "PagePubAudioXMLY",
                        62: "PagePubBabySong",
                        63: "PagePubBaoXiModel",
                        64: "PagePubDietIntegrate",
                        65: "PagePubHuiBenAggregatio",
                        66: "PagePubNinePic",
                        67: "PagePubPicBook",
                        68: "PagePubPosts",
                        69: "PagePubStory",
                        70: "PagePublishStandard",
                        71: "PageRegister",
                        72: "PageRetrievePassword",
                        73: "PageRevenueStats",
                        74: "PageRuleIncome",
                        75: "PageServiceAgreement",
                        76: "PageSettleCenter",
                        77: "PageStatic",
                        78: "PageStaticDetail",
                        79: "PageStaticFans",
                        80: "PageSubsidy",
                        81: "PageSystemInfomationNoticeItem",
                        82: "PageSystemInfomationPrivateMsg",
                        83: "PageSystemInfomationSystemMsg",
                        84: "PageUploadAudio",
                        85: "PageUploadVideo",
                        86: "PageUserType",
                        87: "PageVideo",
                        88: "PageVideoDetail",
                        89: "PubAlbumAliloXMLY",
                        90: "PubAlbumNetease",
                        91: "PubAlbumYiLa",
                        92: "PubAudioAliloXMLY",
                        93: "PubAudioNetease",
                        95: "china",
                        96: "vendors~PageMyPub",
                        97: "vendors~g2"
                    }[e] || e) + "_" + {
                        0: "e7771a47",
                        1: "9257fa72",
                        2: "7bfa78e2",
                        3: "f8fa9434",
                        4: "8fbc523b",
                        5: "3489c5cc",
                        6: "7cc07549",
                        7: "20b38108",
                        8: "a3ef5522",
                        9: "c7e7268f",
                        10: "f785b803",
                        11: "753b7d13",
                        12: "720948d9",
                        13: "c972dbe6",
                        14: "2d7f02cf",
                        15: "b1a47a11",
                        16: "0a29309a",
                        17: "a301eefa",
                        18: "8438a8cc",
                        19: "20a44f7c",
                        20: "281cebcc",
                        21: "1ee48f66",
                        22: "a249a846",
                        23: "fb6e8fa9",
                        24: "6168568b",
                        25: "991499e0",
                        26: "5d593f7c",
                        27: "43fcaec8",
                        28: "4c400ccb",
                        29: "846e158e",
                        30: "d32253b4",
                        31: "3eba6d5c",
                        32: "b0c937a5",
                        33: "8fda3434",
                        34: "d79641aa",
                        35: "e5414c04",
                        36: "c3187803",
                        37: "68952c74",
                        38: "48dec1c1",
                        39: "714e3d71",
                        40: "ed22a0e8",
                        41: "1f704890",
                        42: "bb2ae588",
                        43: "8bd0be0a",
                        44: "352bf1e0",
                        45: "4412ae3a",
                        46: "bf7141ca",
                        47: "5826b64a",
                        48: "cc58390d",
                        49: "b6092d79",
                        50: "a6384a09",
                        51: "e017d5de",
                        52: "33d4ee4c",
                        53: "85f354f6",
                        54: "0ac3528c",
                        55: "6443dd8d",
                        56: "76ad5c03",
                        57: "a1fb1433",
                        58: "843f0602",
                        59: "c83a4bf1",
                        60: "9b06f9c9",
                        61: "d1ace015",
                        62: "2dfc8ab4",
                        63: "87cdc465",
                        64: "5d06bcca",
                        65: "635ddf32",
                        66: "da03c920",
                        67: "f0af8fa8",
                        68: "ee298e3b",
                        69: "d37f5a7e",
                        70: "a2e14a37",
                        71: "ec10c74a",
                        72: "52c40d2a",
                        73: "550c2d9f",
                        74: "f0ae6a2a",
                        75: "9cb3ce2b",
                        76: "399c7378",
                        77: "fcbb700a",
                        78: "f4df4213",
                        79: "ae040623",
                        80: "1203c4d4",
                        81: "58877efe",
                        82: "6248f02e",
                        83: "acd04304",
                        84: "262ff99e",
                        85: "f11a7a8f",
                        86: "daa0a44c",
                        87: "39bfae96",
                        88: "695e5852",
                        89: "2b008cc8",
                        90: "2a19b70f",
                        91: "6a13a4d6",
                        92: "22d716f7",
                        93: "f1c56eb6",
                        95: "668ac214",
                        96: "590d1828",
                        97: "bc379c83"
                    }[e] + ".js"
                }(e),
                0 !== c.src.indexOf(window.location.origin + "/") && (c.crossOrigin = "anonymous");
                var s = new Error;
                u = function(t) {
                    c.onerror = c.onload = null,
                    clearTimeout(l);
                    var n = o[e];
                    if (0 !== n) {
                        if (n) {
                            var r = t && ("load" === t.type ? "missing" : t.type)
                              , i = t && t.target && t.target.src;
                            s.message = "Loading chunk " + e + " failed.\n(" + r + ": " + i + ")",
                            s.name = "ChunkLoadError",
                            s.type = r,
                            s.request = i,
                            n[1](s)
                        }
                        o[e] = void 0
                    }
                }
                ;
                var l = setTimeout((function() {
                    u({
                        type: "timeout",
                        target: c
                    })
                }
                ), 12e4);
                c.onerror = c.onload = u,
                document.head.appendChild(c)
            }
        return Promise.all(t)
    }
    ,
    i.m = e,
    i.c = n,
    i.d = function(e, t, n) {
        i.o(e, t) || Object.defineProperty(e, t, {
            enumerable: !0,
            get: n
        })
    }
    ,
    i.r = function(e) {
        "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
            value: "Module"
        }),
        Object.defineProperty(e, "__esModule", {
            value: !0
        })
    }
    ,
    i.t = function(e, t) {
        if (1 & t && (e = i(e)),
        8 & t)
            return e;
        if (4 & t && "object" === typeof e && e && e.__esModule)
            return e;
        var n = Object.create(null);
        if (i.r(n),
        Object.defineProperty(n, "default", {
            enumerable: !0,
            value: e
        }),
        2 & t && "string" != typeof e)
            for (var r in e)
                i.d(n, r, function(t) {
                    return e[t]
                }
                .bind(null, r));
        return n
    }
    ,
    i.n = function(e) {
        var t = e && e.__esModule ? function() {
            return e.default
        }
        : function() {
            return e
        }
        ;
        return i.d(t, "a", t),
        t
    }
    ,
    i.o = function(e, t) {
        return Object.prototype.hasOwnProperty.call(e, t)
    }
    ,
    i.p = "//h5-content-frontend.babytree.com/",
    i.oe = function(e) {
        throw console.error(e),
        e
    }
    ;
    var a = window.webpackJsonp = window.webpackJsonp || []
      , u = a.push.bind(a);
    a.push = t,
    a = a.slice();
    for (var c = 0; c < a.length; c++)
        t(a[c]);
    var s = u;
    // i(i.s = 196)
    jzq=i
}(
    {
         363: function(t, e, n) {
        "use strict";
        var r = n(890);
        e.a = function(t) {
            var e = new r.JSEncrypt;
            return e.setPublicKey("-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2qC67Y3KF6mupPBsnsoIqEM1dfohMkMI4Rxj60Ae3MOT+Ch3vPZwCj4P5vVw+sVuRv0N94MqraNxLBlQfyeIf2Vu1KOdHD+gFfWneSrNM7Cs4b7Cn+ctCf9tJ239IrLilfsasV6iWc7kDHGIwInMJ9XqqTZTBnWP07SCQYf8J3mL/vw/PY1klBknwh8oLuJi8+BfAS1KPgMuK60NxTAMny+9h9Dno1kVGeLa0Osm4TkVWK9Uyx0XbbV0IfrnbpT/0FUxC6X+K+gHsWzmywrC7145+Bgz0lQo2kRTy551RcyMStlT41poc6ASn8mzCMD4u4MyNU+V0srtFBD8fdwZZwIDAQAB-----END PUBLIC KEY-----"),
            e.encrypt(t)
        }
        // 将加密函数赋值给全局变量enc
        enc = e.a
    },
    890: function(t, e, i) {
        var r, n, s;
        n = [e],
        void 0 === (s = "function" === typeof (r = function(t) {
            var e;
            function i(t, e, i) {
                null != t && ("number" == typeof t ? this.fromNumber(t, e, i) : null == e && "string" != typeof t ? this.fromString(t, 256) : this.fromString(t, e))
            }
            function r() {
                return new i(null)
            }
            "Microsoft Internet Explorer" == navigator.appName ? (i.prototype.am = function(t, e, i, r, n, s) {
                for (var o = 32767 & e, h = e >> 15; --s >= 0; ) {
                    var a = 32767 & this[t]
                      , u = this[t++] >> 15
                      , c = h * a + u * o;
                    n = ((a = o * a + ((32767 & c) << 15) + i[r] + (1073741823 & n)) >>> 30) + (c >>> 15) + h * u + (n >>> 30),
                    i[r++] = 1073741823 & a
                }
                return n
            }
            ,
            e = 30) : "Netscape" != navigator.appName ? (i.prototype.am = function(t, e, i, r, n, s) {
                for (; --s >= 0; ) {
                    var o = e * this[t++] + i[r] + n;
                    n = Math.floor(o / 67108864),
                    i[r++] = 67108863 & o
                }
                return n
            }
            ,
            e = 26) : (i.prototype.am = function(t, e, i, r, n, s) {
                for (var o = 16383 & e, h = e >> 14; --s >= 0; ) {
                    var a = 16383 & this[t]
                      , u = this[t++] >> 14
                      , c = h * a + u * o;
                    n = ((a = o * a + ((16383 & c) << 14) + i[r] + n) >> 28) + (c >> 14) + h * u,
                    i[r++] = 268435455 & a
                }
                return n
            }
            ,
            e = 28),
            i.prototype.DB = e,
            i.prototype.DM = (1 << e) - 1,
            i.prototype.DV = 1 << e,
            i.prototype.FV = Math.pow(2, 52),
            i.prototype.F1 = 52 - e,
            i.prototype.F2 = 2 * e - 52;
            var n, s, o = new Array;
            for (n = "0".charCodeAt(0),
            s = 0; s <= 9; ++s)
                o[n++] = s;
            for (n = "a".charCodeAt(0),
            s = 10; s < 36; ++s)
                o[n++] = s;
            for (n = "A".charCodeAt(0),
            s = 10; s < 36; ++s)
                o[n++] = s;
            function h(t) {
                return "0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)
            }
            function a(t, e) {
                var i = o[t.charCodeAt(e)];
                return null == i ? -1 : i
            }
            function u(t) {
                var e = r();
                return e.fromInt(t),
                e
            }
            function c(t) {
                var e, i = 1;
                return 0 != (e = t >>> 16) && (t = e,
                i += 16),
                0 != (e = t >> 8) && (t = e,
                i += 8),
                0 != (e = t >> 4) && (t = e,
                i += 4),
                0 != (e = t >> 2) && (t = e,
                i += 2),
                0 != (e = t >> 1) && (t = e,
                i += 1),
                i
            }
            function f(t) {
                this.m = t
            }
            function p(t) {
                this.m = t,
                this.mp = t.invDigit(),
                this.mpl = 32767 & this.mp,
                this.mph = this.mp >> 15,
                this.um = (1 << t.DB - 15) - 1,
                this.mt2 = 2 * t.t
            }
            function l(t, e) {
                return t & e
            }
            function d(t, e) {
                return t | e
            }
            function g(t, e) {
                return t ^ e
            }
            function m(t, e) {
                return t & ~e
            }
            function y(t) {
                if (0 == t)
                    return -1;
                var e = 0;
                return 0 == (65535 & t) && (t >>= 16,
                e += 16),
                0 == (255 & t) && (t >>= 8,
                e += 8),
                0 == (15 & t) && (t >>= 4,
                e += 4),
                0 == (3 & t) && (t >>= 2,
                e += 2),
                0 == (1 & t) && ++e,
                e
            }
            function b(t) {
                for (var e = 0; 0 != t; )
                    t &= t - 1,
                    ++e;
                return e
            }
            function T() {}
            function S(t) {
                return t
            }
            function R(t) {
                this.r2 = r(),
                this.q3 = r(),
                i.ONE.dlShiftTo(2 * t.t, this.r2),
                this.mu = this.r2.divide(t),
                this.m = t
            }
            f.prototype.convert = function(t) {
                return t.s < 0 || t.compareTo(this.m) >= 0 ? t.mod(this.m) : t
            }
            ,
            f.prototype.revert = function(t) {
                return t
            }
            ,
            f.prototype.reduce = function(t) {
                t.divRemTo(this.m, null, t)
            }
            ,
            f.prototype.mulTo = function(t, e, i) {
                t.multiplyTo(e, i),
                this.reduce(i)
            }
            ,
            f.prototype.sqrTo = function(t, e) {
                t.squareTo(e),
                this.reduce(e)
            }
            ,
            p.prototype.convert = function(t) {
                var e = r();
                return t.abs().dlShiftTo(this.m.t, e),
                e.divRemTo(this.m, null, e),
                t.s < 0 && e.compareTo(i.ZERO) > 0 && this.m.subTo(e, e),
                e
            }
            ,
            p.prototype.revert = function(t) {
                var e = r();
                return t.copyTo(e),
                this.reduce(e),
                e
            }
            ,
            p.prototype.reduce = function(t) {
                for (; t.t <= this.mt2; )
                    t[t.t++] = 0;
                for (var e = 0; e < this.m.t; ++e) {
                    var i = 32767 & t[e]
                      , r = i * this.mpl + ((i * this.mph + (t[e] >> 15) * this.mpl & this.um) << 15) & t.DM;
                    for (t[i = e + this.m.t] += this.m.am(0, r, t, e, 0, this.m.t); t[i] >= t.DV; )
                        t[i] -= t.DV,
                        t[++i]++
                }
                t.clamp(),
                t.drShiftTo(this.m.t, t),
                t.compareTo(this.m) >= 0 && t.subTo(this.m, t)
            }
            ,
            p.prototype.mulTo = function(t, e, i) {
                t.multiplyTo(e, i),
                this.reduce(i)
            }
            ,
            p.prototype.sqrTo = function(t, e) {
                t.squareTo(e),
                this.reduce(e)
            }
            ,
            i.prototype.copyTo = function(t) {
                for (var e = this.t - 1; e >= 0; --e)
                    t[e] = this[e];
                t.t = this.t,
                t.s = this.s
            }
            ,
            i.prototype.fromInt = function(t) {
                this.t = 1,
                this.s = t < 0 ? -1 : 0,
                t > 0 ? this[0] = t : t < -1 ? this[0] = t + this.DV : this.t = 0
            }
            ,
            i.prototype.fromString = function(t, e) {
                var r;
                if (16 == e)
                    r = 4;
                else if (8 == e)
                    r = 3;
                else if (256 == e)
                    r = 8;
                else if (2 == e)
                    r = 1;
                else if (32 == e)
                    r = 5;
                else {
                    if (4 != e)
                        return void this.fromRadix(t, e);
                    r = 2
                }
                this.t = 0,
                this.s = 0;
                for (var n = t.length, s = !1, o = 0; --n >= 0; ) {
                    var h = 8 == r ? 255 & t[n] : a(t, n);
                    h < 0 ? "-" == t.charAt(n) && (s = !0) : (s = !1,
                    0 == o ? this[this.t++] = h : o + r > this.DB ? (this[this.t - 1] |= (h & (1 << this.DB - o) - 1) << o,
                    this[this.t++] = h >> this.DB - o) : this[this.t - 1] |= h << o,
                    (o += r) >= this.DB && (o -= this.DB))
                }
                8 == r && 0 != (128 & t[0]) && (this.s = -1,
                o > 0 && (this[this.t - 1] |= (1 << this.DB - o) - 1 << o)),
                this.clamp(),
                s && i.ZERO.subTo(this, this)
            }
            ,
            i.prototype.clamp = function() {
                for (var t = this.s & this.DM; this.t > 0 && this[this.t - 1] == t; )
                    --this.t
            }
            ,
            i.prototype.dlShiftTo = function(t, e) {
                var i;
                for (i = this.t - 1; i >= 0; --i)
                    e[i + t] = this[i];
                for (i = t - 1; i >= 0; --i)
                    e[i] = 0;
                e.t = this.t + t,
                e.s = this.s
            }
            ,
            i.prototype.drShiftTo = function(t, e) {
                for (var i = t; i < this.t; ++i)
                    e[i - t] = this[i];
                e.t = Math.max(this.t - t, 0),
                e.s = this.s
            }
            ,
            i.prototype.lShiftTo = function(t, e) {
                var i, r = t % this.DB, n = this.DB - r, s = (1 << n) - 1, o = Math.floor(t / this.DB), h = this.s << r & this.DM;
                for (i = this.t - 1; i >= 0; --i)
                    e[i + o + 1] = this[i] >> n | h,
                    h = (this[i] & s) << r;
                for (i = o - 1; i >= 0; --i)
                    e[i] = 0;
                e[o] = h,
                e.t = this.t + o + 1,
                e.s = this.s,
                e.clamp()
            }
            ,
            i.prototype.rShiftTo = function(t, e) {
                e.s = this.s;
                var i = Math.floor(t / this.DB);
                if (i >= this.t)
                    e.t = 0;
                else {
                    var r = t % this.DB
                      , n = this.DB - r
                      , s = (1 << r) - 1;
                    e[0] = this[i] >> r;
                    for (var o = i + 1; o < this.t; ++o)
                        e[o - i - 1] |= (this[o] & s) << n,
                        e[o - i] = this[o] >> r;
                    r > 0 && (e[this.t - i - 1] |= (this.s & s) << n),
                    e.t = this.t - i,
                    e.clamp()
                }
            }
            ,
            i.prototype.subTo = function(t, e) {
                for (var i = 0, r = 0, n = Math.min(t.t, this.t); i < n; )
                    r += this[i] - t[i],
                    e[i++] = r & this.DM,
                    r >>= this.DB;
                if (t.t < this.t) {
                    for (r -= t.s; i < this.t; )
                        r += this[i],
                        e[i++] = r & this.DM,
                        r >>= this.DB;
                    r += this.s
                } else {
                    for (r += this.s; i < t.t; )
                        r -= t[i],
                        e[i++] = r & this.DM,
                        r >>= this.DB;
                    r -= t.s
                }
                e.s = r < 0 ? -1 : 0,
                r < -1 ? e[i++] = this.DV + r : r > 0 && (e[i++] = r),
                e.t = i,
                e.clamp()
            }
            ,
            i.prototype.multiplyTo = function(t, e) {
                var r = this.abs()
                  , n = t.abs()
                  , s = r.t;
                for (e.t = s + n.t; --s >= 0; )
                    e[s] = 0;
                for (s = 0; s < n.t; ++s)
                    e[s + r.t] = r.am(0, n[s], e, s, 0, r.t);
                e.s = 0,
                e.clamp(),
                this.s != t.s && i.ZERO.subTo(e, e)
            }
            ,
            i.prototype.squareTo = function(t) {
                for (var e = this.abs(), i = t.t = 2 * e.t; --i >= 0; )
                    t[i] = 0;
                for (i = 0; i < e.t - 1; ++i) {
                    var r = e.am(i, e[i], t, 2 * i, 0, 1);
                    (t[i + e.t] += e.am(i + 1, 2 * e[i], t, 2 * i + 1, r, e.t - i - 1)) >= e.DV && (t[i + e.t] -= e.DV,
                    t[i + e.t + 1] = 1)
                }
                t.t > 0 && (t[t.t - 1] += e.am(i, e[i], t, 2 * i, 0, 1)),
                t.s = 0,
                t.clamp()
            }
            ,
            i.prototype.divRemTo = function(t, e, n) {
                var s = t.abs();
                if (!(s.t <= 0)) {
                    var o = this.abs();
                    if (o.t < s.t)
                        return null != e && e.fromInt(0),
                        void (null != n && this.copyTo(n));
                    null == n && (n = r());
                    var h = r()
                      , a = this.s
                      , u = t.s
                      , f = this.DB - c(s[s.t - 1]);
                    f > 0 ? (s.lShiftTo(f, h),
                    o.lShiftTo(f, n)) : (s.copyTo(h),
                    o.copyTo(n));
                    var p = h.t
                      , l = h[p - 1];
                    if (0 != l) {
                        var d = l * (1 << this.F1) + (p > 1 ? h[p - 2] >> this.F2 : 0)
                          , g = this.FV / d
                          , m = (1 << this.F1) / d
                          , y = 1 << this.F2
                          , v = n.t
                          , b = v - p
                          , T = null == e ? r() : e;
                        for (h.dlShiftTo(b, T),
                        n.compareTo(T) >= 0 && (n[n.t++] = 1,
                        n.subTo(T, n)),
                        i.ONE.dlShiftTo(p, T),
                        T.subTo(h, h); h.t < p; )
                            h[h.t++] = 0;
                        for (; --b >= 0; ) {
                            var S = n[--v] == l ? this.DM : Math.floor(n[v] * g + (n[v - 1] + y) * m);
                            if ((n[v] += h.am(0, S, n, b, 0, p)) < S)
                                for (h.dlShiftTo(b, T),
                                n.subTo(T, n); n[v] < --S; )
                                    n.subTo(T, n)
                        }
                        null != e && (n.drShiftTo(p, e),
                        a != u && i.ZERO.subTo(e, e)),
                        n.t = p,
                        n.clamp(),
                        f > 0 && n.rShiftTo(f, n),
                        a < 0 && i.ZERO.subTo(n, n)
                    }
                }
            }
            ,
            i.prototype.invDigit = function() {
                if (this.t < 1)
                    return 0;
                var t = this[0];
                if (0 == (1 & t))
                    return 0;
                var e = 3 & t;
                return (e = (e = (e = (e = e * (2 - (15 & t) * e) & 15) * (2 - (255 & t) * e) & 255) * (2 - ((65535 & t) * e & 65535)) & 65535) * (2 - t * e % this.DV) % this.DV) > 0 ? this.DV - e : -e
            }
            ,
            i.prototype.isEven = function() {
                return 0 == (this.t > 0 ? 1 & this[0] : this.s)
            }
            ,
            i.prototype.exp = function(t, e) {
                if (t > 4294967295 || t < 1)
                    return i.ONE;
                var n = r()
                  , s = r()
                  , o = e.convert(this)
                  , h = c(t) - 1;
                for (o.copyTo(n); --h >= 0; )
                    if (e.sqrTo(n, s),
                    (t & 1 << h) > 0)
                        e.mulTo(s, o, n);
                    else {
                        var a = n;
                        n = s,
                        s = a
                    }
                return e.revert(n)
            }
            ,
            i.prototype.toString = function(t) {
                if (this.s < 0)
                    return "-" + this.negate().toString(t);
                var e;
                if (16 == t)
                    e = 4;
                else if (8 == t)
                    e = 3;
                else if (2 == t)
                    e = 1;
                else if (32 == t)
                    e = 5;
                else {
                    if (4 != t)
                        return this.toRadix(t);
                    e = 2
                }
                var i, r = (1 << e) - 1, n = !1, s = "", o = this.t, a = this.DB - o * this.DB % e;
                if (o-- > 0)
                    for (a < this.DB && (i = this[o] >> a) > 0 && (n = !0,
                    s = h(i)); o >= 0; )
                        a < e ? (i = (this[o] & (1 << a) - 1) << e - a,
                        i |= this[--o] >> (a += this.DB - e)) : (i = this[o] >> (a -= e) & r,
                        a <= 0 && (a += this.DB,
                        --o)),
                        i > 0 && (n = !0),
                        n && (s += h(i));
                return n ? s : "0"
            }
            ,
            i.prototype.negate = function() {
                var t = r();
                return i.ZERO.subTo(this, t),
                t
            }
            ,
            i.prototype.abs = function() {
                return this.s < 0 ? this.negate() : this
            }
            ,
            i.prototype.compareTo = function(t) {
                var e = this.s - t.s;
                if (0 != e)
                    return e;
                var i = this.t;
                if (0 != (e = i - t.t))
                    return this.s < 0 ? -e : e;
                for (; --i >= 0; )
                    if (0 != (e = this[i] - t[i]))
                        return e;
                return 0
            }
            ,
            i.prototype.bitLength = function() {
                return this.t <= 0 ? 0 : this.DB * (this.t - 1) + c(this[this.t - 1] ^ this.s & this.DM)
            }
            ,
            i.prototype.mod = function(t) {
                var e = r();
                return this.abs().divRemTo(t, null, e),
                this.s < 0 && e.compareTo(i.ZERO) > 0 && t.subTo(e, e),
                e
            }
            ,
            i.prototype.modPowInt = function(t, e) {
                var i;
                return i = t < 256 || e.isEven() ? new f(e) : new p(e),
                this.exp(t, i)
            }
            ,
            i.ZERO = u(0),
            i.ONE = u(1),
            T.prototype.convert = S,
            T.prototype.revert = S,
            T.prototype.mulTo = function(t, e, i) {
                t.multiplyTo(e, i)
            }
            ,
            T.prototype.sqrTo = function(t, e) {
                t.squareTo(e)
            }
            ,
            R.prototype.convert = function(t) {
                if (t.s < 0 || t.t > 2 * this.m.t)
                    return t.mod(this.m);
                if (t.compareTo(this.m) < 0)
                    return t;
                var e = r();
                return t.copyTo(e),
                this.reduce(e),
                e
            }
            ,
            R.prototype.revert = function(t) {
                return t
            }
            ,
            R.prototype.reduce = function(t) {
                for (t.drShiftTo(this.m.t - 1, this.r2),
                t.t > this.m.t + 1 && (t.t = this.m.t + 1,
                t.clamp()),
                this.mu.multiplyUpperTo(this.r2, this.m.t + 1, this.q3),
                this.m.multiplyLowerTo(this.q3, this.m.t + 1, this.r2); t.compareTo(this.r2) < 0; )
                    t.dAddOffset(1, this.m.t + 1);
                for (t.subTo(this.r2, t); t.compareTo(this.m) >= 0; )
                    t.subTo(this.m, t)
            }
            ,
            R.prototype.mulTo = function(t, e, i) {
                t.multiplyTo(e, i),
                this.reduce(i)
            }
            ,
            R.prototype.sqrTo = function(t, e) {
                t.squareTo(e),
                this.reduce(e)
            }
            ;
            var E, w, D, x = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521, 523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991, 997], B = (1 << 26) / x[x.length - 1];
            function K() {
                this.i = 0,
                this.j = 0,
                this.S = new Array
            }
            if (i.prototype.chunkSize = function(t) {
                return Math.floor(Math.LN2 * this.DB / Math.log(t))
            }
            ,
            i.prototype.toRadix = function(t) {
                if (null == t && (t = 10),
                0 == this.signum() || t < 2 || t > 36)
                    return "0";
                var e = this.chunkSize(t)
                  , i = Math.pow(t, e)
                  , n = u(i)
                  , s = r()
                  , o = r()
                  , h = "";
                for (this.divRemTo(n, s, o); s.signum() > 0; )
                    h = (i + o.intValue()).toString(t).substr(1) + h,
                    s.divRemTo(n, s, o);
                return o.intValue().toString(t) + h
            }
            ,
            i.prototype.fromRadix = function(t, e) {
                this.fromInt(0),
                null == e && (e = 10);
                for (var r = this.chunkSize(e), n = Math.pow(e, r), s = !1, o = 0, h = 0, u = 0; u < t.length; ++u) {
                    var c = a(t, u);
                    c < 0 ? "-" == t.charAt(u) && 0 == this.signum() && (s = !0) : (h = e * h + c,
                    ++o >= r && (this.dMultiply(n),
                    this.dAddOffset(h, 0),
                    o = 0,
                    h = 0))
                }
                o > 0 && (this.dMultiply(Math.pow(e, o)),
                this.dAddOffset(h, 0)),
                s && i.ZERO.subTo(this, this)
            }
            ,
            i.prototype.fromNumber = function(t, e, r) {
                if ("number" == typeof e)
                    if (t < 2)
                        this.fromInt(1);
                    else
                        for (this.fromNumber(t, r),
                        this.testBit(t - 1) || this.bitwiseTo(i.ONE.shiftLeft(t - 1), d, this),
                        this.isEven() && this.dAddOffset(1, 0); !this.isProbablePrime(e); )
                            this.dAddOffset(2, 0),
                            this.bitLength() > t && this.subTo(i.ONE.shiftLeft(t - 1), this);
                else {
                    var n = new Array
                      , s = 7 & t;
                    n.length = 1 + (t >> 3),
                    e.nextBytes(n),
                    s > 0 ? n[0] &= (1 << s) - 1 : n[0] = 0,
                    this.fromString(n, 256)
                }
            }
            ,
            i.prototype.bitwiseTo = function(t, e, i) {
                var r, n, s = Math.min(t.t, this.t);
                for (r = 0; r < s; ++r)
                    i[r] = e(this[r], t[r]);
                if (t.t < this.t) {
                    for (n = t.s & this.DM,
                    r = s; r < this.t; ++r)
                        i[r] = e(this[r], n);
                    i.t = this.t
                } else {
                    for (n = this.s & this.DM,
                    r = s; r < t.t; ++r)
                        i[r] = e(n, t[r]);
                    i.t = t.t
                }
                i.s = e(this.s, t.s),
                i.clamp()
            }
            ,
            i.prototype.changeBit = function(t, e) {
                var r = i.ONE.shiftLeft(t);
                return this.bitwiseTo(r, e, r),
                r
            }
            ,
            i.prototype.addTo = function(t, e) {
                for (var i = 0, r = 0, n = Math.min(t.t, this.t); i < n; )
                    r += this[i] + t[i],
                    e[i++] = r & this.DM,
                    r >>= this.DB;
                if (t.t < this.t) {
                    for (r += t.s; i < this.t; )
                        r += this[i],
                        e[i++] = r & this.DM,
                        r >>= this.DB;
                    r += this.s
                } else {
                    for (r += this.s; i < t.t; )
                        r += t[i],
                        e[i++] = r & this.DM,
                        r >>= this.DB;
                    r += t.s
                }
                e.s = r < 0 ? -1 : 0,
                r > 0 ? e[i++] = r : r < -1 && (e[i++] = this.DV + r),
                e.t = i,
                e.clamp()
            }
            ,
            i.prototype.dMultiply = function(t) {
                this[this.t] = this.am(0, t - 1, this, 0, 0, this.t),
                ++this.t,
                this.clamp()
            }
            ,
            i.prototype.dAddOffset = function(t, e) {
                if (0 != t) {
                    for (; this.t <= e; )
                        this[this.t++] = 0;
                    for (this[e] += t; this[e] >= this.DV; )
                        this[e] -= this.DV,
                        ++e >= this.t && (this[this.t++] = 0),
                        ++this[e]
                }
            }
            ,
            i.prototype.multiplyLowerTo = function(t, e, i) {
                var r, n = Math.min(this.t + t.t, e);
                for (i.s = 0,
                i.t = n; n > 0; )
                    i[--n] = 0;
                for (r = i.t - this.t; n < r; ++n)
                    i[n + this.t] = this.am(0, t[n], i, n, 0, this.t);
                for (r = Math.min(t.t, e); n < r; ++n)
                    this.am(0, t[n], i, n, 0, e - n);
                i.clamp()
            }
            ,
            i.prototype.multiplyUpperTo = function(t, e, i) {
                --e;
                var r = i.t = this.t + t.t - e;
                for (i.s = 0; --r >= 0; )
                    i[r] = 0;
                for (r = Math.max(e - this.t, 0); r < t.t; ++r)
                    i[this.t + r - e] = this.am(e - r, t[r], i, 0, 0, this.t + r - e);
                i.clamp(),
                i.drShiftTo(1, i)
            }
            ,
            i.prototype.modInt = function(t) {
                if (t <= 0)
                    return 0;
                var e = this.DV % t
                  , i = this.s < 0 ? t - 1 : 0;
                if (this.t > 0)
                    if (0 == e)
                        i = this[0] % t;
                    else
                        for (var r = this.t - 1; r >= 0; --r)
                            i = (e * i + this[r]) % t;
                return i
            }
            ,
            i.prototype.millerRabin = function(t) {
                var e = this.subtract(i.ONE)
                  , n = e.getLowestSetBit();
                if (n <= 0)
                    return !1;
                var s = e.shiftRight(n);
                (t = t + 1 >> 1) > x.length && (t = x.length);
                for (var o = r(), h = 0; h < t; ++h) {
                    o.fromInt(x[Math.floor(Math.random() * x.length)]);
                    var a = o.modPow(s, this);
                    if (0 != a.compareTo(i.ONE) && 0 != a.compareTo(e)) {
                        for (var u = 1; u++ < n && 0 != a.compareTo(e); )
                            if (0 == (a = a.modPowInt(2, this)).compareTo(i.ONE))
                                return !1;
                        if (0 != a.compareTo(e))
                            return !1
                    }
                }
                return !0
            }
            ,
            i.prototype.clone = function() {
                var t = r();
                return this.copyTo(t),
                t
            }
            ,
            i.prototype.intValue = function() {
                if (this.s < 0) {
                    if (1 == this.t)
                        return this[0] - this.DV;
                    if (0 == this.t)
                        return -1
                } else {
                    if (1 == this.t)
                        return this[0];
                    if (0 == this.t)
                        return 0
                }
                return (this[1] & (1 << 32 - this.DB) - 1) << this.DB | this[0]
            }
            ,
            i.prototype.byteValue = function() {
                return 0 == this.t ? this.s : this[0] << 24 >> 24
            }
            ,
            i.prototype.shortValue = function() {
                return 0 == this.t ? this.s : this[0] << 16 >> 16
            }
            ,
            i.prototype.signum = function() {
                return this.s < 0 ? -1 : this.t <= 0 || 1 == this.t && this[0] <= 0 ? 0 : 1
            }
            ,
            i.prototype.toByteArray = function() {
                var t = this.t
                  , e = new Array;
                e[0] = this.s;
                var i, r = this.DB - t * this.DB % 8, n = 0;
                if (t-- > 0)
                    for (r < this.DB && (i = this[t] >> r) != (this.s & this.DM) >> r && (e[n++] = i | this.s << this.DB - r); t >= 0; )
                        r < 8 ? (i = (this[t] & (1 << r) - 1) << 8 - r,
                        i |= this[--t] >> (r += this.DB - 8)) : (i = this[t] >> (r -= 8) & 255,
                        r <= 0 && (r += this.DB,
                        --t)),
                        0 != (128 & i) && (i |= -256),
                        0 == n && (128 & this.s) != (128 & i) && ++n,
                        (n > 0 || i != this.s) && (e[n++] = i);
                return e
            }
            ,
            i.prototype.equals = function(t) {
                return 0 == this.compareTo(t)
            }
            ,
            i.prototype.min = function(t) {
                return this.compareTo(t) < 0 ? this : t
            }
            ,
            i.prototype.max = function(t) {
                return this.compareTo(t) > 0 ? this : t
            }
            ,
            i.prototype.and = function(t) {
                var e = r();
                return this.bitwiseTo(t, l, e),
                e
            }
            ,
            i.prototype.or = function(t) {
                var e = r();
                return this.bitwiseTo(t, d, e),
                e
            }
            ,
            i.prototype.xor = function(t) {
                var e = r();
                return this.bitwiseTo(t, g, e),
                e
            }
            ,
            i.prototype.andNot = function(t) {
                var e = r();
                return this.bitwiseTo(t, m, e),
                e
            }
            ,
            i.prototype.not = function() {
                for (var t = r(), e = 0; e < this.t; ++e)
                    t[e] = this.DM & ~this[e];
                return t.t = this.t,
                t.s = ~this.s,
                t
            }
            ,
            i.prototype.shiftLeft = function(t) {
                var e = r();
                return t < 0 ? this.rShiftTo(-t, e) : this.lShiftTo(t, e),
                e
            }
            ,
            i.prototype.shiftRight = function(t) {
                var e = r();
                return t < 0 ? this.lShiftTo(-t, e) : this.rShiftTo(t, e),
                e
            }
            ,
            i.prototype.getLowestSetBit = function() {
                for (var t = 0; t < this.t; ++t)
                    if (0 != this[t])
                        return t * this.DB + y(this[t]);
                return this.s < 0 ? this.t * this.DB : -1
            }
            ,
            i.prototype.bitCount = function() {
                for (var t = 0, e = this.s & this.DM, i = 0; i < this.t; ++i)
                    t += b(this[i] ^ e);
                return t
            }
            ,
            i.prototype.testBit = function(t) {
                var e = Math.floor(t / this.DB);
                return e >= this.t ? 0 != this.s : 0 != (this[e] & 1 << t % this.DB)
            }
            ,
            i.prototype.setBit = function(t) {
                return this.changeBit(t, d)
            }
            ,
            i.prototype.clearBit = function(t) {
                return this.changeBit(t, m)
            }
            ,
            i.prototype.flipBit = function(t) {
                return this.changeBit(t, g)
            }
            ,
            i.prototype.add = function(t) {
                var e = r();
                return this.addTo(t, e),
                e
            }
            ,
            i.prototype.subtract = function(t) {
                var e = r();
                return this.subTo(t, e),
                e
            }
            ,
            i.prototype.multiply = function(t) {
                var e = r();
                return this.multiplyTo(t, e),
                e
            }
            ,
            i.prototype.divide = function(t) {
                var e = r();
                return this.divRemTo(t, e, null),
                e
            }
            ,
            i.prototype.remainder = function(t) {
                var e = r();
                return this.divRemTo(t, null, e),
                e
            }
            ,
            i.prototype.divideAndRemainder = function(t) {
                var e = r()
                  , i = r();
                return this.divRemTo(t, e, i),
                new Array(e,i)
            }
            ,
            i.prototype.modPow = function(t, e) {
                var i, n, s = t.bitLength(), o = u(1);
                if (s <= 0)
                    return o;
                i = s < 18 ? 1 : s < 48 ? 3 : s < 144 ? 4 : s < 768 ? 5 : 6,
                n = s < 8 ? new f(e) : e.isEven() ? new R(e) : new p(e);
                var h = new Array
                  , a = 3
                  , l = i - 1
                  , d = (1 << i) - 1;
                if (h[1] = n.convert(this),
                i > 1) {
                    var g = r();
                    for (n.sqrTo(h[1], g); a <= d; )
                        h[a] = r(),
                        n.mulTo(g, h[a - 2], h[a]),
                        a += 2
                }
                var m, y, v = t.t - 1, b = !0, T = r();
                for (s = c(t[v]) - 1; v >= 0; ) {
                    for (s >= l ? m = t[v] >> s - l & d : (m = (t[v] & (1 << s + 1) - 1) << l - s,
                    v > 0 && (m |= t[v - 1] >> this.DB + s - l)),
                    a = i; 0 == (1 & m); )
                        m >>= 1,
                        --a;
                    if ((s -= a) < 0 && (s += this.DB,
                    --v),
                    b)
                        h[m].copyTo(o),
                        b = !1;
                    else {
                        for (; a > 1; )
                            n.sqrTo(o, T),
                            n.sqrTo(T, o),
                            a -= 2;
                        a > 0 ? n.sqrTo(o, T) : (y = o,
                        o = T,
                        T = y),
                        n.mulTo(T, h[m], o)
                    }
                    for (; v >= 0 && 0 == (t[v] & 1 << s); )
                        n.sqrTo(o, T),
                        y = o,
                        o = T,
                        T = y,
                        --s < 0 && (s = this.DB - 1,
                        --v)
                }
                return n.revert(o)
            }
            ,
            i.prototype.modInverse = function(t) {
                var e = t.isEven();
                if (this.isEven() && e || 0 == t.signum())
                    return i.ZERO;
                for (var r = t.clone(), n = this.clone(), s = u(1), o = u(0), h = u(0), a = u(1); 0 != r.signum(); ) {
                    for (; r.isEven(); )
                        r.rShiftTo(1, r),
                        e ? (s.isEven() && o.isEven() || (s.addTo(this, s),
                        o.subTo(t, o)),
                        s.rShiftTo(1, s)) : o.isEven() || o.subTo(t, o),
                        o.rShiftTo(1, o);
                    for (; n.isEven(); )
                        n.rShiftTo(1, n),
                        e ? (h.isEven() && a.isEven() || (h.addTo(this, h),
                        a.subTo(t, a)),
                        h.rShiftTo(1, h)) : a.isEven() || a.subTo(t, a),
                        a.rShiftTo(1, a);
                    r.compareTo(n) >= 0 ? (r.subTo(n, r),
                    e && s.subTo(h, s),
                    o.subTo(a, o)) : (n.subTo(r, n),
                    e && h.subTo(s, h),
                    a.subTo(o, a))
                }
                return 0 != n.compareTo(i.ONE) ? i.ZERO : a.compareTo(t) >= 0 ? a.subtract(t) : a.signum() < 0 ? (a.addTo(t, a),
                a.signum() < 0 ? a.add(t) : a) : a
            }
            ,
            i.prototype.pow = function(t) {
                return this.exp(t, new T)
            }
            ,
            i.prototype.gcd = function(t) {
                var e = this.s < 0 ? this.negate() : this.clone()
                  , i = t.s < 0 ? t.negate() : t.clone();
                if (e.compareTo(i) < 0) {
                    var r = e;
                    e = i,
                    i = r
                }
                var n = e.getLowestSetBit()
                  , s = i.getLowestSetBit();
                if (s < 0)
                    return e;
                for (n < s && (s = n),
                s > 0 && (e.rShiftTo(s, e),
                i.rShiftTo(s, i)); e.signum() > 0; )
                    (n = e.getLowestSetBit()) > 0 && e.rShiftTo(n, e),
                    (n = i.getLowestSetBit()) > 0 && i.rShiftTo(n, i),
                    e.compareTo(i) >= 0 ? (e.subTo(i, e),
                    e.rShiftTo(1, e)) : (i.subTo(e, i),
                    i.rShiftTo(1, i));
                return s > 0 && i.lShiftTo(s, i),
                i
            }
            ,
            i.prototype.isProbablePrime = function(t) {
                var e, i = this.abs();
                if (1 == i.t && i[0] <= x[x.length - 1]) {
                    for (e = 0; e < x.length; ++e)
                        if (i[0] == x[e])
                            return !0;
                    return !1
                }
                if (i.isEven())
                    return !1;
                for (e = 1; e < x.length; ) {
                    for (var r = x[e], n = e + 1; n < x.length && r < B; )
                        r *= x[n++];
                    for (r = i.modInt(r); e < n; )
                        if (r % x[e++] == 0)
                            return !1
                }
                return i.millerRabin(t)
            }
            ,
            i.prototype.square = function() {
                var t = r();
                return this.squareTo(t),
                t
            }
            ,
            K.prototype.init = function(t) {
                var e, i, r;
                for (e = 0; e < 256; ++e)
                    this.S[e] = e;
                for (i = 0,
                e = 0; e < 256; ++e)
                    i = i + this.S[e] + t[e % t.length] & 255,
                    r = this.S[e],
                    this.S[e] = this.S[i],
                    this.S[i] = r;
                this.i = 0,
                this.j = 0
            }
            ,
            K.prototype.next = function() {
                var t;
                return this.i = this.i + 1 & 255,
                this.j = this.j + this.S[this.i] & 255,
                t = this.S[this.i],
                this.S[this.i] = this.S[this.j],
                this.S[this.j] = t,
                this.S[t + this.S[this.i] & 255]
            }
            ,
            null == w) {
                var A;
                if (w = new Array,
                D = 0,
                window.crypto && window.crypto.getRandomValues) {
                    var O = new Uint32Array(256);
                    for (window.crypto.getRandomValues(O),
                    A = 0; A < O.length; ++A)
                        w[D++] = 255 & O[A]
                }
                var U = function(t) {
                    if (this.count = this.count || 0,
                    this.count >= 256 || D >= 256)
                        window.removeEventListener ? window.removeEventListener("mousemove", U, !1) : window.detachEvent && window.detachEvent("onmousemove", U);
                    else
                        try {
                            var e = t.x + t.y;
                            w[D++] = 255 & e,
                            this.count += 1
                        } catch (i) {}
                };
                window.addEventListener ? window.addEventListener("mousemove", U, !1) : window.attachEvent && window.attachEvent("onmousemove", U)
            }
            function V() {
                if (null == E) {
                    for (E = new K; D < 256; ) {
                        var t = Math.floor(65536 * Math.random());
                        w[D++] = 255 & t
                    }
                    for (E.init(w),
                    D = 0; D < w.length; ++D)
                        w[D] = 0;
                    D = 0
                }
                return E.next()
            }
            function J() {}
            function N(t, e) {
                return new i(t,e)
            }
            function I() {
                this.n = null,
                this.e = 0,
                this.d = null,
                this.p = null,
                this.q = null,
                this.dmp1 = null,
                this.dmq1 = null,
                this.coeff = null
            }
            J.prototype.nextBytes = function(t) {
                var e;
                for (e = 0; e < t.length; ++e)
                    t[e] = V()
            }
            ,
            I.prototype.doPublic = function(t) {
                return t.modPowInt(this.e, this.n)
            }
            ,
            I.prototype.setPublic = function(t, e) {
                null != t && null != e && t.length > 0 && e.length > 0 ? (this.n = N(t, 16),
                this.e = parseInt(e, 16)) : console.error("Invalid RSA public key")
            }
            ,
            I.prototype.encrypt = function(t) {
                var e = function(t, e) {
                    if (e < t.length + 11)
                        return console.error("Message too long for RSA"),
                        null;
                    for (var r = new Array, n = t.length - 1; n >= 0 && e > 0; ) {
                        var s = t.charCodeAt(n--);
                        s < 128 ? r[--e] = s : s > 127 && s < 2048 ? (r[--e] = 63 & s | 128,
                        r[--e] = s >> 6 | 192) : (r[--e] = 63 & s | 128,
                        r[--e] = s >> 6 & 63 | 128,
                        r[--e] = s >> 12 | 224)
                    }
                    r[--e] = 0;
                    for (var o = new J, h = new Array; e > 2; ) {
                        for (h[0] = 0; 0 == h[0]; )
                            o.nextBytes(h);
                        r[--e] = h[0]
                    }
                    return r[--e] = 2,
                    r[--e] = 0,
                    new i(r)
                }(t, this.n.bitLength() + 7 >> 3);
                if (null == e)
                    return null;
                var r = this.doPublic(e);
                if (null == r)
                    return null;
                var n = r.toString(16);
                return 0 == (1 & n.length) ? n : "0" + n
            }
            ,
            I.prototype.doPrivate = function(t) {
                if (null == this.p || null == this.q)
                    return t.modPow(this.d, this.n);
                for (var e = t.mod(this.p).modPow(this.dmp1, this.p), i = t.mod(this.q).modPow(this.dmq1, this.q); e.compareTo(i) < 0; )
                    e = e.add(this.p);
                return e.subtract(i).multiply(this.coeff).mod(this.p).multiply(this.q).add(i)
            }
            ,
            I.prototype.setPrivate = function(t, e, i) {
                null != t && null != e && t.length > 0 && e.length > 0 ? (this.n = N(t, 16),
                this.e = parseInt(e, 16),
                this.d = N(i, 16)) : console.error("Invalid RSA private key")
            }
            ,
            I.prototype.setPrivateEx = function(t, e, i, r, n, s, o, h) {
                null != t && null != e && t.length > 0 && e.length > 0 ? (this.n = N(t, 16),
                this.e = parseInt(e, 16),
                this.d = N(i, 16),
                this.p = N(r, 16),
                this.q = N(n, 16),
                this.dmp1 = N(s, 16),
                this.dmq1 = N(o, 16),
                this.coeff = N(h, 16)) : console.error("Invalid RSA private key")
            }
            ,
            I.prototype.generate = function(t, e) {
                var r = new J
                  , n = t >> 1;
                this.e = parseInt(e, 16);
                for (var s = new i(e,16); ; ) {
                    for (; this.p = new i(t - n,1,r),
                    0 != this.p.subtract(i.ONE).gcd(s).compareTo(i.ONE) || !this.p.isProbablePrime(10); )
                        ;
                    for (; this.q = new i(n,1,r),
                    0 != this.q.subtract(i.ONE).gcd(s).compareTo(i.ONE) || !this.q.isProbablePrime(10); )
                        ;
                    if (this.p.compareTo(this.q) <= 0) {
                        var o = this.p;
                        this.p = this.q,
                        this.q = o
                    }
                    var h = this.p.subtract(i.ONE)
                      , a = this.q.subtract(i.ONE)
                      , u = h.multiply(a);
                    if (0 == u.gcd(s).compareTo(i.ONE)) {
                        this.n = this.p.multiply(this.q),
                        this.d = s.modInverse(u),
                        this.dmp1 = this.d.mod(h),
                        this.dmq1 = this.d.mod(a),
                        this.coeff = this.q.modInverse(this.p);
                        break
                    }
                }
            }
            ,
            I.prototype.decrypt = function(t) {
                var e = N(t, 16)
                  , i = this.doPrivate(e);
                return null == i ? null : function(t, e) {
                    for (var i = t.toByteArray(), r = 0; r < i.length && 0 == i[r]; )
                        ++r;
                    if (i.length - r != e - 1 || 2 != i[r])
                        return null;
                    for (++r; 0 != i[r]; )
                        if (++r >= i.length)
                            return null;
                    for (var n = ""; ++r < i.length; ) {
                        var s = 255 & i[r];
                        s < 128 ? n += String.fromCharCode(s) : s > 191 && s < 224 ? (n += String.fromCharCode((31 & s) << 6 | 63 & i[r + 1]),
                        ++r) : (n += String.fromCharCode((15 & s) << 12 | (63 & i[r + 1]) << 6 | 63 & i[r + 2]),
                        r += 2)
                    }
                    return n
                }(i, this.n.bitLength() + 7 >> 3)
            }
            ,
            I.prototype.generateAsync = function(t, e, n) {
                var s = new J
                  , o = t >> 1;
                this.e = parseInt(e, 16);
                var h = new i(e,16)
                  , a = this
                  , u = function() {
                    var e = function() {
                        if (a.p.compareTo(a.q) <= 0) {
                            var t = a.p;
                            a.p = a.q,
                            a.q = t
                        }
                        var e = a.p.subtract(i.ONE)
                          , r = a.q.subtract(i.ONE)
                          , s = e.multiply(r);
                        0 == s.gcd(h).compareTo(i.ONE) ? (a.n = a.p.multiply(a.q),
                        a.d = h.modInverse(s),
                        a.dmp1 = a.d.mod(e),
                        a.dmq1 = a.d.mod(r),
                        a.coeff = a.q.modInverse(a.p),
                        setTimeout((function() {
                            n()
                        }
                        ), 0)) : setTimeout(u, 0)
                    }
                      , c = function() {
                        a.q = r(),
                        a.q.fromNumberAsync(o, 1, s, (function() {
                            a.q.subtract(i.ONE).gcda(h, (function(t) {
                                0 == t.compareTo(i.ONE) && a.q.isProbablePrime(10) ? setTimeout(e, 0) : setTimeout(c, 0)
                            }
                            ))
                        }
                        ))
                    }
                      , f = function() {
                        a.p = r(),
                        a.p.fromNumberAsync(t - o, 1, s, (function() {
                            a.p.subtract(i.ONE).gcda(h, (function(t) {
                                0 == t.compareTo(i.ONE) && a.p.isProbablePrime(10) ? setTimeout(c, 0) : setTimeout(f, 0)
                            }
                            ))
                        }
                        ))
                    };
                    setTimeout(f, 0)
                };
                setTimeout(u, 0)
            }
            ,
            i.prototype.gcda = function(t, e) {
                var i = this.s < 0 ? this.negate() : this.clone()
                  , r = t.s < 0 ? t.negate() : t.clone();
                if (i.compareTo(r) < 0) {
                    var n = i;
                    i = r,
                    r = n
                }
                var s = i.getLowestSetBit()
                  , o = r.getLowestSetBit();
                if (o < 0)
                    e(i);
                else {
                    s < o && (o = s),
                    o > 0 && (i.rShiftTo(o, i),
                    r.rShiftTo(o, r));
                    var h = function() {
                        (s = i.getLowestSetBit()) > 0 && i.rShiftTo(s, i),
                        (s = r.getLowestSetBit()) > 0 && r.rShiftTo(s, r),
                        i.compareTo(r) >= 0 ? (i.subTo(r, i),
                        i.rShiftTo(1, i)) : (r.subTo(i, r),
                        r.rShiftTo(1, r)),
                        i.signum() > 0 ? setTimeout(h, 0) : (o > 0 && r.lShiftTo(o, r),
                        setTimeout((function() {
                            e(r)
                        }
                        ), 0))
                    };
                    setTimeout(h, 10)
                }
            }
            ,
            i.prototype.fromNumberAsync = function(t, e, r, n) {
                if ("number" == typeof e)
                    if (t < 2)
                        this.fromInt(1);
                    else {
                        this.fromNumber(t, r),
                        this.testBit(t - 1) || this.bitwiseTo(i.ONE.shiftLeft(t - 1), d, this),
                        this.isEven() && this.dAddOffset(1, 0);
                        var s = this
                          , o = function() {
                            s.dAddOffset(2, 0),
                            s.bitLength() > t && s.subTo(i.ONE.shiftLeft(t - 1), s),
                            s.isProbablePrime(e) ? setTimeout((function() {
                                n()
                            }
                            ), 0) : setTimeout(o, 0)
                        };
                        setTimeout(o, 0)
                    }
                else {
                    var h = new Array
                      , a = 7 & t;
                    h.length = 1 + (t >> 3),
                    e.nextBytes(h),
                    a > 0 ? h[0] &= (1 << a) - 1 : h[0] = 0,
                    this.fromString(h, 256)
                }
            }
            ;
            var P = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
            function M(t) {
                var e, i, r = "";
                for (e = 0; e + 3 <= t.length; e += 3)
                    i = parseInt(t.substring(e, e + 3), 16),
                    r += P.charAt(i >> 6) + P.charAt(63 & i);
                for (e + 1 == t.length ? (i = parseInt(t.substring(e, e + 1), 16),
                r += P.charAt(i << 2)) : e + 2 == t.length && (i = parseInt(t.substring(e, e + 2), 16),
                r += P.charAt(i >> 2) + P.charAt((3 & i) << 4)); (3 & r.length) > 0; )
                    r += "=";
                return r
            }
            function L(t) {
                var e, i, r = "", n = 0;
                for (e = 0; e < t.length && "=" != t.charAt(e); ++e)
                    v = P.indexOf(t.charAt(e)),
                    v < 0 || (0 == n ? (r += h(v >> 2),
                    i = 3 & v,
                    n = 1) : 1 == n ? (r += h(i << 2 | v >> 4),
                    i = 15 & v,
                    n = 2) : 2 == n ? (r += h(i),
                    r += h(v >> 2),
                    i = 3 & v,
                    n = 3) : (r += h(i << 2 | v >> 4),
                    r += h(15 & v),
                    n = 0));
                return 1 == n && (r += h(i << 2)),
                r
            }
            var q = q || {};
            q.env = q.env || {};
            var C = q
              , H = Object.prototype
              , j = ["toString", "valueOf"];
            q.env.parseUA = function(t) {
                var e, i = function(t) {
                    var e = 0;
                    return parseFloat(t.replace(/\./g, (function() {
                        return 1 == e++ ? "" : "."
                    }
                    )))
                }, r = navigator, n = {
                    ie: 0,
                    opera: 0,
                    gecko: 0,
                    webkit: 0,
                    chrome: 0,
                    mobile: null,
                    air: 0,
                    ipad: 0,
                    iphone: 0,
                    ipod: 0,
                    ios: null,
                    android: 0,
                    webos: 0,
                    caja: r && r.cajaVersion,
                    secure: !1,
                    os: null
                }, s = t || navigator && navigator.userAgent, o = window && window.location, h = o && o.href;
                return n.secure = h && 0 === h.toLowerCase().indexOf("https"),
                s && (/windows|win32/i.test(s) ? n.os = "windows" : /macintosh/i.test(s) ? n.os = "macintosh" : /rhino/i.test(s) && (n.os = "rhino"),
                /KHTML/.test(s) && (n.webkit = 1),
                (e = s.match(/AppleWebKit\/([^\s]*)/)) && e[1] && (n.webkit = i(e[1]),
                / Mobile\//.test(s) ? (n.mobile = "Apple",
                (e = s.match(/OS ([^\s]*)/)) && e[1] && (e = i(e[1].replace("_", "."))),
                n.ios = e,
                n.ipad = n.ipod = n.iphone = 0,
                (e = s.match(/iPad|iPod|iPhone/)) && e[0] && (n[e[0].toLowerCase()] = n.ios)) : ((e = s.match(/NokiaN[^\/]*|Android \d\.\d|webOS\/\d\.\d/)) && (n.mobile = e[0]),
                /webOS/.test(s) && (n.mobile = "WebOS",
                (e = s.match(/webOS\/([^\s]*);/)) && e[1] && (n.webos = i(e[1]))),
                / Android/.test(s) && (n.mobile = "Android",
                (e = s.match(/Android ([^\s]*);/)) && e[1] && (n.android = i(e[1])))),
                (e = s.match(/Chrome\/([^\s]*)/)) && e[1] ? n.chrome = i(e[1]) : (e = s.match(/AdobeAIR\/([^\s]*)/)) && (n.air = e[0])),
                n.webkit || ((e = s.match(/Opera[\s\/]([^\s]*)/)) && e[1] ? (n.opera = i(e[1]),
                (e = s.match(/Version\/([^\s]*)/)) && e[1] && (n.opera = i(e[1])),
                (e = s.match(/Opera Mini[^;]*/)) && (n.mobile = e[0])) : (e = s.match(/MSIE\s([^;]*)/)) && e[1] ? n.ie = i(e[1]) : (e = s.match(/Gecko\/([^\s]*)/)) && (n.gecko = 1,
                (e = s.match(/rv:([^\s\)]*)/)) && e[1] && (n.gecko = i(e[1]))))),
                n
            }
            ,
            q.env.ua = q.env.parseUA(),
            q.isFunction = function(t) {
                return "function" === typeof t || "[object Function]" === H.toString.apply(t)
            }
            ,
            q._IEEnumFix = q.env.ua.ie ? function(t, e) {
                var i, r, n;
                for (i = 0; i < j.length; i += 1)
                    n = e[r = j[i]],
                    C.isFunction(n) && n != H[r] && (t[r] = n)
            }
            : function() {}
            ,
            q.extend = function(t, e, i) {
                if (!e || !t)
                    throw new Error("extend failed, please check that all dependencies are included.");
                var r, n = function() {};
                if (n.prototype = e.prototype,
                t.prototype = new n,
                t.prototype.constructor = t,
                t.superclass = e.prototype,
                e.prototype.constructor == H.constructor && (e.prototype.constructor = e),
                i) {
                    for (r in i)
                        C.hasOwnProperty(i, r) && (t.prototype[r] = i[r]);
                    C._IEEnumFix(t.prototype, i)
                }
            }
            ,
            "undefined" != typeof KJUR && KJUR || (KJUR = {}),
            "undefined" != typeof KJUR.asn1 && KJUR.asn1 || (KJUR.asn1 = {}),
            KJUR.asn1.ASN1Util = new function() {
                this.integerToByteHex = function(t) {
                    var e = t.toString(16);
                    return e.length % 2 == 1 && (e = "0" + e),
                    e
                }
                ,
                this.bigIntToMinTwosComplementsHex = function(t) {
                    var e = t.toString(16);
                    if ("-" != e.substr(0, 1))
                        e.length % 2 == 1 ? e = "0" + e : e.match(/^[0-7]/) || (e = "00" + e);
                    else {
                        var r = e.substr(1).length;
                        r % 2 == 1 ? r += 1 : e.match(/^[0-7]/) || (r += 2);
                        for (var n = "", s = 0; s < r; s++)
                            n += "f";
                        e = new i(n,16).xor(t).add(i.ONE).toString(16).replace(/^-/, "")
                    }
                    return e
                }
                ,
                this.getPEMStringFromHex = function(t, e) {
                    var i = CryptoJS.enc.Hex.parse(t)
                      , r = CryptoJS.enc.Base64.stringify(i).replace(/(.{64})/g, "$1\r\n");
                    return "-----BEGIN " + e + "-----\r\n" + (r = r.replace(/\r\n$/, "")) + "\r\n-----END " + e + "-----\r\n"
                }
            }
            ,
            KJUR.asn1.ASN1Object = function() {
                this.getLengthHexFromValue = function() {
                    if ("undefined" == typeof this.hV || null == this.hV)
                        throw "this.hV is null or undefined.";
                    if (this.hV.length % 2 == 1)
                        throw "value hex must be even length: n=" + "".length + ",v=" + this.hV;
                    var t = this.hV.length / 2
                      , e = t.toString(16);
                    if (e.length % 2 == 1 && (e = "0" + e),
                    t < 128)
                        return e;
                    var i = e.length / 2;
                    if (i > 15)
                        throw "ASN.1 length too long to represent by 8x: n = " + t.toString(16);
                    return (128 + i).toString(16) + e
                }
                ,
                this.getEncodedHex = function() {
                    return (null == this.hTLV || this.isModified) && (this.hV = this.getFreshValueHex(),
                    this.hL = this.getLengthHexFromValue(),
                    this.hTLV = this.hT + this.hL + this.hV,
                    this.isModified = !1),
                    this.hTLV
                }
                ,
                this.getValueHex = function() {
                    return this.getEncodedHex(),
                    this.hV
                }
                ,
                this.getFreshValueHex = function() {
                    return ""
                }
            }
            ,
            KJUR.asn1.DERAbstractString = function(t) {
                KJUR.asn1.DERAbstractString.superclass.constructor.call(this),
                this.getString = function() {
                    return this.s
                }
                ,
                this.setString = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.s = t,
                    this.hV = stohex(this.s)
                }
                ,
                this.setStringHex = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.s = null,
                    this.hV = t
                }
                ,
                this.getFreshValueHex = function() {
                    return this.hV
                }
                ,
                "undefined" != typeof t && ("undefined" != typeof t.str ? this.setString(t.str) : "undefined" != typeof t.hex && this.setStringHex(t.hex))
            }
            ,
            q.extend(KJUR.asn1.DERAbstractString, KJUR.asn1.ASN1Object),
            KJUR.asn1.DERAbstractTime = function(t) {
                KJUR.asn1.DERAbstractTime.superclass.constructor.call(this),
                this.localDateToUTC = function(t) {
                    return utc = t.getTime() + 6e4 * t.getTimezoneOffset(),
                    new Date(utc)
                }
                ,
                this.formatDate = function(t, e) {
                    var i = this.zeroPadding
                      , r = this.localDateToUTC(t)
                      , n = String(r.getFullYear());
                    return "utc" == e && (n = n.substr(2, 2)),
                    n + i(String(r.getMonth() + 1), 2) + i(String(r.getDate()), 2) + i(String(r.getHours()), 2) + i(String(r.getMinutes()), 2) + i(String(r.getSeconds()), 2) + "Z"
                }
                ,
                this.zeroPadding = function(t, e) {
                    return t.length >= e ? t : new Array(e - t.length + 1).join("0") + t
                }
                ,
                this.getString = function() {
                    return this.s
                }
                ,
                this.setString = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.s = t,
                    this.hV = stohex(this.s)
                }
                ,
                this.setByDateValue = function(t, e, i, r, n, s) {
                    var o = new Date(Date.UTC(t, e - 1, i, r, n, s, 0));
                    this.setByDate(o)
                }
                ,
                this.getFreshValueHex = function() {
                    return this.hV
                }
            }
            ,
            q.extend(KJUR.asn1.DERAbstractTime, KJUR.asn1.ASN1Object),
            KJUR.asn1.DERAbstractStructured = function(t) {
                KJUR.asn1.DERAbstractString.superclass.constructor.call(this),
                this.setByASN1ObjectArray = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.asn1Array = t
                }
                ,
                this.appendASN1Object = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.asn1Array.push(t)
                }
                ,
                this.asn1Array = new Array,
                "undefined" != typeof t && "undefined" != typeof t.array && (this.asn1Array = t.array)
            }
            ,
            q.extend(KJUR.asn1.DERAbstractStructured, KJUR.asn1.ASN1Object),
            KJUR.asn1.DERBoolean = function() {
                KJUR.asn1.DERBoolean.superclass.constructor.call(this),
                this.hT = "01",
                this.hTLV = "0101ff"
            }
            ,
            q.extend(KJUR.asn1.DERBoolean, KJUR.asn1.ASN1Object),
            KJUR.asn1.DERInteger = function(t) {
                KJUR.asn1.DERInteger.superclass.constructor.call(this),
                this.hT = "02",
                this.setByBigInteger = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.hV = KJUR.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)
                }
                ,
                this.setByInteger = function(t) {
                    var e = new i(String(t),10);
                    this.setByBigInteger(e)
                }
                ,
                this.setValueHex = function(t) {
                    this.hV = t
                }
                ,
                this.getFreshValueHex = function() {
                    return this.hV
                }
                ,
                "undefined" != typeof t && ("undefined" != typeof t.bigint ? this.setByBigInteger(t.bigint) : "undefined" != typeof t.int ? this.setByInteger(t.int) : "undefined" != typeof t.hex && this.setValueHex(t.hex))
            }
            ,
            q.extend(KJUR.asn1.DERInteger, KJUR.asn1.ASN1Object),
            KJUR.asn1.DERBitString = function(t) {
                KJUR.asn1.DERBitString.superclass.constructor.call(this),
                this.hT = "03",
                this.setHexValueIncludingUnusedBits = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.hV = t
                }
                ,
                this.setUnusedBitsAndHexValue = function(t, e) {
                    if (t < 0 || 7 < t)
                        throw "unused bits shall be from 0 to 7: u = " + t;
                    var i = "0" + t;
                    this.hTLV = null,
                    this.isModified = !0,
                    this.hV = i + e
                }
                ,
                this.setByBinaryString = function(t) {
                    var e = 8 - (t = t.replace(/0+$/, "")).length % 8;
                    8 == e && (e = 0);
                    for (var i = 0; i <= e; i++)
                        t += "0";
                    var r = "";
                    for (i = 0; i < t.length - 1; i += 8) {
                        var n = t.substr(i, 8)
                          , s = parseInt(n, 2).toString(16);
                        1 == s.length && (s = "0" + s),
                        r += s
                    }
                    this.hTLV = null,
                    this.isModified = !0,
                    this.hV = "0" + e + r
                }
                ,
                this.setByBooleanArray = function(t) {
                    for (var e = "", i = 0; i < t.length; i++)
                        1 == t[i] ? e += "1" : e += "0";
                    this.setByBinaryString(e)
                }
                ,
                this.newFalseArray = function(t) {
                    for (var e = new Array(t), i = 0; i < t; i++)
                        e[i] = !1;
                    return e
                }
                ,
                this.getFreshValueHex = function() {
                    return this.hV
                }
                ,
                "undefined" != typeof t && ("undefined" != typeof t.hex ? this.setHexValueIncludingUnusedBits(t.hex) : "undefined" != typeof t.bin ? this.setByBinaryString(t.bin) : "undefined" != typeof t.array && this.setByBooleanArray(t.array))
            }
            ,
            q.extend(KJUR.asn1.DERBitString, KJUR.asn1.ASN1Object),
            KJUR.asn1.DEROctetString = function(t) {
                KJUR.asn1.DEROctetString.superclass.constructor.call(this, t),
                this.hT = "04"
            }
            ,
            q.extend(KJUR.asn1.DEROctetString, KJUR.asn1.DERAbstractString),
            KJUR.asn1.DERNull = function() {
                KJUR.asn1.DERNull.superclass.constructor.call(this),
                this.hT = "05",
                this.hTLV = "0500"
            }
            ,
            q.extend(KJUR.asn1.DERNull, KJUR.asn1.ASN1Object),
            KJUR.asn1.DERObjectIdentifier = function(t) {
                var e = function(t) {
                    var e = t.toString(16);
                    return 1 == e.length && (e = "0" + e),
                    e
                }
                  , r = function(t) {
                    var r = ""
                      , n = new i(t,10).toString(2)
                      , s = 7 - n.length % 7;
                    7 == s && (s = 0);
                    for (var o = "", h = 0; h < s; h++)
                        o += "0";
                    for (n = o + n,
                    h = 0; h < n.length - 1; h += 7) {
                        var a = n.substr(h, 7);
                        h != n.length - 7 && (a = "1" + a),
                        r += e(parseInt(a, 2))
                    }
                    return r
                };
                KJUR.asn1.DERObjectIdentifier.superclass.constructor.call(this),
                this.hT = "06",
                this.setValueHex = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.s = null,
                    this.hV = t
                }
                ,
                this.setValueOidString = function(t) {
                    if (!t.match(/^[0-9.]+$/))
                        throw "malformed oid string: " + t;
                    var i = ""
                      , n = t.split(".")
                      , s = 40 * parseInt(n[0]) + parseInt(n[1]);
                    i += e(s),
                    n.splice(0, 2);
                    for (var o = 0; o < n.length; o++)
                        i += r(n[o]);
                    this.hTLV = null,
                    this.isModified = !0,
                    this.s = null,
                    this.hV = i
                }
                ,
                this.setValueName = function(t) {
                    if ("undefined" == typeof KJUR.asn1.x509.OID.name2oidList[t])
                        throw "DERObjectIdentifier oidName undefined: " + t;
                    var e = KJUR.asn1.x509.OID.name2oidList[t];
                    this.setValueOidString(e)
                }
                ,
                this.getFreshValueHex = function() {
                    return this.hV
                }
                ,
                "undefined" != typeof t && ("undefined" != typeof t.oid ? this.setValueOidString(t.oid) : "undefined" != typeof t.hex ? this.setValueHex(t.hex) : "undefined" != typeof t.name && this.setValueName(t.name))
            }
            ,
            q.extend(KJUR.asn1.DERObjectIdentifier, KJUR.asn1.ASN1Object),
            KJUR.asn1.DERUTF8String = function(t) {
                KJUR.asn1.DERUTF8String.superclass.constructor.call(this, t),
                this.hT = "0c"
            }
            ,
            q.extend(KJUR.asn1.DERUTF8String, KJUR.asn1.DERAbstractString),
            KJUR.asn1.DERNumericString = function(t) {
                KJUR.asn1.DERNumericString.superclass.constructor.call(this, t),
                this.hT = "12"
            }
            ,
            q.extend(KJUR.asn1.DERNumericString, KJUR.asn1.DERAbstractString),
            KJUR.asn1.DERPrintableString = function(t) {
                KJUR.asn1.DERPrintableString.superclass.constructor.call(this, t),
                this.hT = "13"
            }
            ,
            q.extend(KJUR.asn1.DERPrintableString, KJUR.asn1.DERAbstractString),
            KJUR.asn1.DERTeletexString = function(t) {
                KJUR.asn1.DERTeletexString.superclass.constructor.call(this, t),
                this.hT = "14"
            }
            ,
            q.extend(KJUR.asn1.DERTeletexString, KJUR.asn1.DERAbstractString),
            KJUR.asn1.DERIA5String = function(t) {
                KJUR.asn1.DERIA5String.superclass.constructor.call(this, t),
                this.hT = "16"
            }
            ,
            q.extend(KJUR.asn1.DERIA5String, KJUR.asn1.DERAbstractString),
            KJUR.asn1.DERUTCTime = function(t) {
                KJUR.asn1.DERUTCTime.superclass.constructor.call(this, t),
                this.hT = "17",
                this.setByDate = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.date = t,
                    this.s = this.formatDate(this.date, "utc"),
                    this.hV = stohex(this.s)
                }
                ,
                "undefined" != typeof t && ("undefined" != typeof t.str ? this.setString(t.str) : "undefined" != typeof t.hex ? this.setStringHex(t.hex) : "undefined" != typeof t.date && this.setByDate(t.date))
            }
            ,
            q.extend(KJUR.asn1.DERUTCTime, KJUR.asn1.DERAbstractTime),
            KJUR.asn1.DERGeneralizedTime = function(t) {
                KJUR.asn1.DERGeneralizedTime.superclass.constructor.call(this, t),
                this.hT = "18",
                this.setByDate = function(t) {
                    this.hTLV = null,
                    this.isModified = !0,
                    this.date = t,
                    this.s = this.formatDate(this.date, "gen"),
                    this.hV = stohex(this.s)
                }
                ,
                "undefined" != typeof t && ("undefined" != typeof t.str ? this.setString(t.str) : "undefined" != typeof t.hex ? this.setStringHex(t.hex) : "undefined" != typeof t.date && this.setByDate(t.date))
            }
            ,
            q.extend(KJUR.asn1.DERGeneralizedTime, KJUR.asn1.DERAbstractTime),
            KJUR.asn1.DERSequence = function(t) {
                KJUR.asn1.DERSequence.superclass.constructor.call(this, t),
                this.hT = "30",
                this.getFreshValueHex = function() {
                    for (var t = "", e = 0; e < this.asn1Array.length; e++)
                        t += this.asn1Array[e].getEncodedHex();
                    return this.hV = t,
                    this.hV
                }
            }
            ,
            q.extend(KJUR.asn1.DERSequence, KJUR.asn1.DERAbstractStructured),
            KJUR.asn1.DERSet = function(t) {
                KJUR.asn1.DERSet.superclass.constructor.call(this, t),
                this.hT = "31",
                this.getFreshValueHex = function() {
                    for (var t = new Array, e = 0; e < this.asn1Array.length; e++) {
                        var i = this.asn1Array[e];
                        t.push(i.getEncodedHex())
                    }
                    return t.sort(),
                    this.hV = t.join(""),
                    this.hV
                }
            }
            ,
            q.extend(KJUR.asn1.DERSet, KJUR.asn1.DERAbstractStructured),
            KJUR.asn1.DERTaggedObject = function(t) {
                KJUR.asn1.DERTaggedObject.superclass.constructor.call(this),
                this.hT = "a0",
                this.hV = "",
                this.isExplicit = !0,
                this.asn1Object = null,
                this.setASN1Object = function(t, e, i) {
                    this.hT = e,
                    this.isExplicit = t,
                    this.asn1Object = i,
                    this.isExplicit ? (this.hV = this.asn1Object.getEncodedHex(),
                    this.hTLV = null,
                    this.isModified = !0) : (this.hV = null,
                    this.hTLV = i.getEncodedHex(),
                    this.hTLV = this.hTLV.replace(/^../, e),
                    this.isModified = !1)
                }
                ,
                this.getFreshValueHex = function() {
                    return this.hV
                }
                ,
                "undefined" != typeof t && ("undefined" != typeof t.tag && (this.hT = t.tag),
                "undefined" != typeof t.explicit && (this.isExplicit = t.explicit),
                "undefined" != typeof t.obj && (this.asn1Object = t.obj,
                this.setASN1Object(this.isExplicit, this.hT, this.asn1Object)))
            }
            ,
            q.extend(KJUR.asn1.DERTaggedObject, KJUR.asn1.ASN1Object),
            function(t) {
                "use strict";
                var e, i = {
                    decode: function(t) {
                        var i;
                        if (void 0 === e) {
                            var r = "0123456789ABCDEF";
                            for (e = [],
                            i = 0; i < 16; ++i)
                                e[r.charAt(i)] = i;
                            for (r = r.toLowerCase(),
                            i = 10; i < 16; ++i)
                                e[r.charAt(i)] = i;
                            for (i = 0; i < " \f\n\r\t \u2028\u2029".length; ++i)
                                e[" \f\n\r\t \u2028\u2029".charAt(i)] = -1
                        }
                        var n = []
                          , s = 0
                          , o = 0;
                        for (i = 0; i < t.length; ++i) {
                            var h = t.charAt(i);
                            if ("=" == h)
                                break;
                            if (-1 != (h = e[h])) {
                                if (void 0 === h)
                                    throw "Illegal character at offset " + i;
                                s |= h,
                                ++o >= 2 ? (n[n.length] = s,
                                s = 0,
                                o = 0) : s <<= 4
                            }
                        }
                        if (o)
                            throw "Hex encoding incomplete: 4 bits missing";
                        return n
                    }
                };
                window.Hex = i
            }(),
            function(t) {
                "use strict";
                var e, i = {
                    decode: function(t) {
                        var i;
                        if (void 0 === e) {
                            for (e = [],
                            i = 0; i < 64; ++i)
                                e["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i)] = i;
                            for (i = 0; i < "= \f\n\r\t \u2028\u2029".length; ++i)
                                e["= \f\n\r\t \u2028\u2029".charAt(i)] = -1
                        }
                        var r = []
                          , n = 0
                          , s = 0;
                        for (i = 0; i < t.length; ++i) {
                            var o = t.charAt(i);
                            if ("=" == o)
                                break;
                            if (-1 != (o = e[o])) {
                                if (void 0 === o)
                                    throw "Illegal character at offset " + i;
                                n |= o,
                                ++s >= 4 ? (r[r.length] = n >> 16,
                                r[r.length] = n >> 8 & 255,
                                r[r.length] = 255 & n,
                                n = 0,
                                s = 0) : n <<= 6
                            }
                        }
                        switch (s) {
                        case 1:
                            throw "Base64 encoding incomplete: at least 2 bits missing";
                        case 2:
                            r[r.length] = n >> 10;
                            break;
                        case 3:
                            r[r.length] = n >> 16,
                            r[r.length] = n >> 8 & 255
                        }
                        return r
                    },
                    re: /-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,
                    unarmor: function(t) {
                        var e = i.re.exec(t);
                        if (e)
                            if (e[1])
                                t = e[1];
                            else {
                                if (!e[2])
                                    throw "RegExp out of sync";
                                t = e[2]
                            }
                        return i.decode(t)
                    }
                };
                window.Base64 = i
            }(),
            function(t) {
                "use strict";
                var e = function(t, e) {
                    var i = document.createElement(t);
                    return i.className = e,
                    i
                }
                  , i = function(t) {
                    return document.createTextNode(t)
                };
                function r(t, e) {
                    t instanceof r ? (this.enc = t.enc,
                    this.pos = t.pos) : (this.enc = t,
                    this.pos = e)
                }
                function n(t, e, i, r, n) {
                    this.stream = t,
                    this.header = e,
                    this.length = i,
                    this.tag = r,
                    this.sub = n
                }
                r.prototype.get = function(t) {
                    if (void 0 === t && (t = this.pos++),
                    t >= this.enc.length)
                        throw "Requesting byte offset " + t + " on a stream of length " + this.enc.length;
                    return this.enc[t]
                }
                ,
                r.prototype.hexDigits = "0123456789ABCDEF",
                r.prototype.hexByte = function(t) {
                    return this.hexDigits.charAt(t >> 4 & 15) + this.hexDigits.charAt(15 & t)
                }
                ,
                r.prototype.hexDump = function(t, e, i) {
                    for (var r = "", n = t; n < e; ++n)
                        if (r += this.hexByte(this.get(n)),
                        !0 !== i)
                            switch (15 & n) {
                            case 7:
                                r += "  ";
                                break;
                            case 15:
                                r += "\n";
                                break;
                            default:
                                r += " "
                            }
                    return r
                }
                ,
                r.prototype.parseStringISO = function(t, e) {
                    for (var i = "", r = t; r < e; ++r)
                        i += String.fromCharCode(this.get(r));
                    return i
                }
                ,
                r.prototype.parseStringUTF = function(t, e) {
                    for (var i = "", r = t; r < e; ) {
                        var n = this.get(r++);
                        i += n < 128 ? String.fromCharCode(n) : n > 191 && n < 224 ? String.fromCharCode((31 & n) << 6 | 63 & this.get(r++)) : String.fromCharCode((15 & n) << 12 | (63 & this.get(r++)) << 6 | 63 & this.get(r++))
                    }
                    return i
                }
                ,
                r.prototype.parseStringBMP = function(t, e) {
                    for (var i = "", r = t; r < e; r += 2) {
                        var n = this.get(r)
                          , s = this.get(r + 1);
                        i += String.fromCharCode((n << 8) + s)
                    }
                    return i
                }
                ,
                r.prototype.reTime = /^((?:1[89]|2\d)?\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,
                r.prototype.parseTime = function(t, e) {
                    var i = this.parseStringISO(t, e)
                      , r = this.reTime.exec(i);
                    return r ? (i = r[1] + "-" + r[2] + "-" + r[3] + " " + r[4],
                    r[5] && (i += ":" + r[5],
                    r[6] && (i += ":" + r[6],
                    r[7] && (i += "." + r[7]))),
                    r[8] && (i += " UTC",
                    "Z" != r[8] && (i += r[8],
                    r[9] && (i += ":" + r[9]))),
                    i) : "Unrecognized time: " + i
                }
                ,
                r.prototype.parseInteger = function(t, e) {
                    var i = e - t;
                    if (i > 4) {
                        i <<= 3;
                        var r = this.get(t);
                        if (0 === r)
                            i -= 8;
                        else
                            for (; r < 128; )
                                r <<= 1,
                                --i;
                        return "(" + i + " bit)"
                    }
                    for (var n = 0, s = t; s < e; ++s)
                        n = n << 8 | this.get(s);
                    return n
                }
                ,
                r.prototype.parseBitString = function(t, e) {
                    var i = this.get(t)
                      , r = (e - t - 1 << 3) - i
                      , n = "(" + r + " bit)";
                    if (r <= 20) {
                        var s = i;
                        n += " ";
                        for (var o = e - 1; o > t; --o) {
                            for (var h = this.get(o), a = s; a < 8; ++a)
                                n += h >> a & 1 ? "1" : "0";
                            s = 0
                        }
                    }
                    return n
                }
                ,
                r.prototype.parseOctetString = function(t, e) {
                    var i = e - t
                      , r = "(" + i + " byte) ";
                    i > 100 && (e = t + 100);
                    for (var n = t; n < e; ++n)
                        r += this.hexByte(this.get(n));
                    return i > 100 && (r += "…"),
                    r
                }
                ,
                r.prototype.parseOID = function(t, e) {
                    for (var i = "", r = 0, n = 0, s = t; s < e; ++s) {
                        var o = this.get(s);
                        if (r = r << 7 | 127 & o,
                        n += 7,
                        !(128 & o)) {
                            if ("" === i) {
                                var h = r < 80 ? r < 40 ? 0 : 1 : 2;
                                i = h + "." + (r - 40 * h)
                            } else
                                i += "." + (n >= 31 ? "bigint" : r);
                            r = n = 0
                        }
                    }
                    return i
                }
                ,
                n.prototype.typeName = function() {
                    if (void 0 === this.tag)
                        return "unknown";
                    var t = this.tag >> 6
                      , e = (this.tag,
                    31 & this.tag);
                    switch (t) {
                    case 0:
                        switch (e) {
                        case 0:
                            return "EOC";
                        case 1:
                            return "BOOLEAN";
                        case 2:
                            return "INTEGER";
                        case 3:
                            return "BIT_STRING";
                        case 4:
                            return "OCTET_STRING";
                        case 5:
                            return "NULL";
                        case 6:
                            return "OBJECT_IDENTIFIER";
                        case 7:
                            return "ObjectDescriptor";
                        case 8:
                            return "EXTERNAL";
                        case 9:
                            return "REAL";
                        case 10:
                            return "ENUMERATED";
                        case 11:
                            return "EMBEDDED_PDV";
                        case 12:
                            return "UTF8String";
                        case 16:
                            return "SEQUENCE";
                        case 17:
                            return "SET";
                        case 18:
                            return "NumericString";
                        case 19:
                            return "PrintableString";
                        case 20:
                            return "TeletexString";
                        case 21:
                            return "VideotexString";
                        case 22:
                            return "IA5String";
                        case 23:
                            return "UTCTime";
                        case 24:
                            return "GeneralizedTime";
                        case 25:
                            return "GraphicString";
                        case 26:
                            return "VisibleString";
                        case 27:
                            return "GeneralString";
                        case 28:
                            return "UniversalString";
                        case 30:
                            return "BMPString";
                        default:
                            return "Universal_" + e.toString(16)
                        }
                    case 1:
                        return "Application_" + e.toString(16);
                    case 2:
                        return "[" + e + "]";
                    case 3:
                        return "Private_" + e.toString(16)
                    }
                }
                ,
                n.prototype.reSeemsASCII = /^[ -~]+$/,
                n.prototype.content = function() {
                    if (void 0 === this.tag)
                        return null;
                    var t = this.tag >> 6
                      , e = 31 & this.tag
                      , i = this.posContent()
                      , r = Math.abs(this.length);
                    if (0 !== t) {
                        if (null !== this.sub)
                            return "(" + this.sub.length + " elem)";
                        var n = this.stream.parseStringISO(i, i + Math.min(r, 100));
                        return this.reSeemsASCII.test(n) ? n.substring(0, 200) + (n.length > 200 ? "…" : "") : this.stream.parseOctetString(i, i + r)
                    }
                    switch (e) {
                    case 1:
                        return 0 === this.stream.get(i) ? "false" : "true";
                    case 2:
                        return this.stream.parseInteger(i, i + r);
                    case 3:
                        return this.sub ? "(" + this.sub.length + " elem)" : this.stream.parseBitString(i, i + r);
                    case 4:
                        return this.sub ? "(" + this.sub.length + " elem)" : this.stream.parseOctetString(i, i + r);
                    case 6:
                        return this.stream.parseOID(i, i + r);
                    case 16:
                    case 17:
                        return "(" + this.sub.length + " elem)";
                    case 12:
                        return this.stream.parseStringUTF(i, i + r);
                    case 18:
                    case 19:
                    case 20:
                    case 21:
                    case 22:
                    case 26:
                        return this.stream.parseStringISO(i, i + r);
                    case 30:
                        return this.stream.parseStringBMP(i, i + r);
                    case 23:
                    case 24:
                        return this.stream.parseTime(i, i + r)
                    }
                    return null
                }
                ,
                n.prototype.toString = function() {
                    return this.typeName() + "@" + this.stream.pos + "[header:" + this.header + ",length:" + this.length + ",sub:" + (null === this.sub ? "null" : this.sub.length) + "]"
                }
                ,
                n.prototype.print = function(t) {
                    if (void 0 === t && (t = ""),
                    document.writeln(t + this),
                    null !== this.sub) {
                        t += "  ";
                        for (var e = 0, i = this.sub.length; e < i; ++e)
                            this.sub[e].print(t)
                    }
                }
                ,
                n.prototype.toPrettyString = function(t) {
                    void 0 === t && (t = "");
                    var e = t + this.typeName() + " @" + this.stream.pos;
                    if (this.length >= 0 && (e += "+"),
                    e += this.length,
                    32 & this.tag ? e += " (constructed)" : 3 != this.tag && 4 != this.tag || null === this.sub || (e += " (encapsulates)"),
                    e += "\n",
                    null !== this.sub) {
                        t += "  ";
                        for (var i = 0, r = this.sub.length; i < r; ++i)
                            e += this.sub[i].toPrettyString(t)
                    }
                    return e
                }
                ,
                n.prototype.toDOM = function() {
                    var t = e("div", "node");
                    t.asn1 = this;
                    var r = e("div", "head")
                      , n = this.typeName().replace(/_/g, " ");
                    r.innerHTML = n;
                    var s = this.content();
                    if (null !== s) {
                        s = String(s).replace(/</g, "&lt;");
                        var o = e("span", "preview");
                        o.appendChild(i(s)),
                        r.appendChild(o)
                    }
                    t.appendChild(r),
                    this.node = t,
                    this.head = r;
                    var h = e("div", "value");
                    if (n = "Offset: " + this.stream.pos + "<br/>",
                    n += "Length: " + this.header + "+",
                    this.length >= 0 ? n += this.length : n += -this.length + " (undefined)",
                    32 & this.tag ? n += "<br/>(constructed)" : 3 != this.tag && 4 != this.tag || null === this.sub || (n += "<br/>(encapsulates)"),
                    null !== s && (n += "<br/>Value:<br/><b>" + s + "</b>",
                    "object" === typeof oids && 6 == this.tag)) {
                        var a = oids[s];
                        a && (a.d && (n += "<br/>" + a.d),
                        a.c && (n += "<br/>" + a.c),
                        a.w && (n += "<br/>(warning!)"))
                    }
                    h.innerHTML = n,
                    t.appendChild(h);
                    var u = e("div", "sub");
                    if (null !== this.sub)
                        for (var c = 0, f = this.sub.length; c < f; ++c)
                            u.appendChild(this.sub[c].toDOM());
                    return t.appendChild(u),
                    r.onclick = function() {
                        t.className = "node collapsed" == t.className ? "node" : "node collapsed"
                    }
                    ,
                    t
                }
                ,
                n.prototype.posStart = function() {
                    return this.stream.pos
                }
                ,
                n.prototype.posContent = function() {
                    return this.stream.pos + this.header
                }
                ,
                n.prototype.posEnd = function() {
                    return this.stream.pos + this.header + Math.abs(this.length)
                }
                ,
                n.prototype.fakeHover = function(t) {
                    this.node.className += " hover",
                    t && (this.head.className += " hover")
                }
                ,
                n.prototype.fakeOut = function(t) {
                    var e = / ?hover/;
                    this.node.className = this.node.className.replace(e, ""),
                    t && (this.head.className = this.head.className.replace(e, ""))
                }
                ,
                n.prototype.toHexDOM_sub = function(t, r, n, s, o) {
                    if (!(s >= o)) {
                        var h = e("span", r);
                        h.appendChild(i(n.hexDump(s, o))),
                        t.appendChild(h)
                    }
                }
                ,
                n.prototype.toHexDOM = function(t) {
                    var r = e("span", "hex");
                    if (void 0 === t && (t = r),
                    this.head.hexNode = r,
                    this.head.onmouseover = function() {
                        this.hexNode.className = "hexCurrent"
                    }
                    ,
                    this.head.onmouseout = function() {
                        this.hexNode.className = "hex"
                    }
                    ,
                    r.asn1 = this,
                    r.onmouseover = function() {
                        var e = !t.selected;
                        e && (t.selected = this.asn1,
                        this.className = "hexCurrent"),
                        this.asn1.fakeHover(e)
                    }
                    ,
                    r.onmouseout = function() {
                        var e = t.selected == this.asn1;
                        this.asn1.fakeOut(e),
                        e && (t.selected = null,
                        this.className = "hex")
                    }
                    ,
                    this.toHexDOM_sub(r, "tag", this.stream, this.posStart(), this.posStart() + 1),
                    this.toHexDOM_sub(r, this.length >= 0 ? "dlen" : "ulen", this.stream, this.posStart() + 1, this.posContent()),
                    null === this.sub)
                        r.appendChild(i(this.stream.hexDump(this.posContent(), this.posEnd())));
                    else if (this.sub.length > 0) {
                        var n = this.sub[0]
                          , s = this.sub[this.sub.length - 1];
                        this.toHexDOM_sub(r, "intro", this.stream, this.posContent(), n.posStart());
                        for (var o = 0, h = this.sub.length; o < h; ++o)
                            r.appendChild(this.sub[o].toHexDOM(t));
                        this.toHexDOM_sub(r, "outro", this.stream, s.posEnd(), this.posEnd())
                    }
                    return r
                }
                ,
                n.prototype.toHexString = function(t) {
                    return this.stream.hexDump(this.posStart(), this.posEnd(), !0)
                }
                ,
                n.decodeLength = function(t) {
                    var e = t.get()
                      , i = 127 & e;
                    if (i == e)
                        return i;
                    if (i > 3)
                        throw "Length over 24 bits not supported at position " + (t.pos - 1);
                    if (0 === i)
                        return -1;
                    e = 0;
                    for (var r = 0; r < i; ++r)
                        e = e << 8 | t.get();
                    return e
                }
                ,
                n.hasContent = function(t, e, i) {
                    if (32 & t)
                        return !0;
                    if (t < 3 || t > 4)
                        return !1;
                    var s = new r(i);
                    if (3 == t && s.get(),
                    s.get() >> 6 & 1)
                        return !1;
                    try {
                        var o = n.decodeLength(s);
                        return s.pos - i.pos + o == e
                    } catch (h) {
                        return !1
                    }
                }
                ,
                n.decode = function(t) {
                    t instanceof r || (t = new r(t,0));
                    var e = new r(t)
                      , i = t.get()
                      , s = n.decodeLength(t)
                      , o = t.pos - e.pos
                      , h = null;
                    if (n.hasContent(i, s, t)) {
                        var a = t.pos;
                        if (3 == i && t.get(),
                        h = [],
                        s >= 0) {
                            for (var u = a + s; t.pos < u; )
                                h[h.length] = n.decode(t);
                            if (t.pos != u)
                                throw "Content size is not correct for container starting at offset " + a
                        } else
                            try {
                                for (; ; ) {
                                    var c = n.decode(t);
                                    if (0 === c.tag)
                                        break;
                                    h[h.length] = c
                                }
                                s = a - t.pos
                            } catch (f) {
                                throw "Exception while decoding undefined length content: " + f
                            }
                    } else
                        t.pos += s;
                    return new n(e,o,s,i,h)
                }
                ,
                n.test = function() {
                    for (var t = [{
                        value: [39],
                        expected: 39
                    }, {
                        value: [129, 201],
                        expected: 201
                    }, {
                        value: [131, 254, 220, 186],
                        expected: 16702650
                    }], e = 0, i = t.length; e < i; ++e) {
                        var s = new r(t[e].value,0)
                          , o = n.decodeLength(s);
                        o != t[e].expected && document.write("In test[" + e + "] expected " + t[e].expected + " got " + o + "\n")
                    }
                }
                ,
                window.ASN1 = n
            }(),
            ASN1.prototype.getHexStringValue = function() {
                var t = this.toHexString()
                  , e = 2 * this.header
                  , i = 2 * this.length;
                return t.substr(e, i)
            }
            ,
            I.prototype.parseKey = function(t) {
                try {
                    var e = 0
                      , i = 0
                      , r = /^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t) ? Hex.decode(t) : Base64.unarmor(t)
                      , n = ASN1.decode(r);
                    if (3 === n.sub.length && (n = n.sub[2].sub[0]),
                    9 === n.sub.length) {
                        e = n.sub[1].getHexStringValue(),
                        this.n = N(e, 16),
                        i = n.sub[2].getHexStringValue(),
                        this.e = parseInt(i, 16);
                        var s = n.sub[3].getHexStringValue();
                        this.d = N(s, 16);
                        var o = n.sub[4].getHexStringValue();
                        this.p = N(o, 16);
                        var h = n.sub[5].getHexStringValue();
                        this.q = N(h, 16);
                        var a = n.sub[6].getHexStringValue();
                        this.dmp1 = N(a, 16);
                        var u = n.sub[7].getHexStringValue();
                        this.dmq1 = N(u, 16);
                        var c = n.sub[8].getHexStringValue();
                        this.coeff = N(c, 16)
                    } else {
                        if (2 !== n.sub.length)
                            return !1;
                        var f = n.sub[1].sub[0];
                        e = f.sub[0].getHexStringValue(),
                        this.n = N(e, 16),
                        i = f.sub[1].getHexStringValue(),
                        this.e = parseInt(i, 16)
                    }
                    return !0
                } catch (p) {
                    return !1
                }
            }
            ,
            I.prototype.getPrivateBaseKey = function() {
                var t = {
                    array: [new KJUR.asn1.DERInteger({
                        int: 0
                    }), new KJUR.asn1.DERInteger({
                        bigint: this.n
                    }), new KJUR.asn1.DERInteger({
                        int: this.e
                    }), new KJUR.asn1.DERInteger({
                        bigint: this.d
                    }), new KJUR.asn1.DERInteger({
                        bigint: this.p
                    }), new KJUR.asn1.DERInteger({
                        bigint: this.q
                    }), new KJUR.asn1.DERInteger({
                        bigint: this.dmp1
                    }), new KJUR.asn1.DERInteger({
                        bigint: this.dmq1
                    }), new KJUR.asn1.DERInteger({
                        bigint: this.coeff
                    })]
                };
                return new KJUR.asn1.DERSequence(t).getEncodedHex()
            }
            ,
            I.prototype.getPrivateBaseKeyB64 = function() {
                return M(this.getPrivateBaseKey())
            }
            ,
            I.prototype.getPublicBaseKey = function() {
                var t = {
                    array: [new KJUR.asn1.DERObjectIdentifier({
                        oid: "1.2.840.113549.1.1.1"
                    }), new KJUR.asn1.DERNull]
                }
                  , e = new KJUR.asn1.DERSequence(t);
                return t = {
                    array: [new KJUR.asn1.DERInteger({
                        bigint: this.n
                    }), new KJUR.asn1.DERInteger({
                        int: this.e
                    })]
                },
                t = {
                    hex: "00" + new KJUR.asn1.DERSequence(t).getEncodedHex()
                },
                t = {
                    array: [e, new KJUR.asn1.DERBitString(t)]
                },
                new KJUR.asn1.DERSequence(t).getEncodedHex()
            }
            ,
            I.prototype.getPublicBaseKeyB64 = function() {
                return M(this.getPublicBaseKey())
            }
            ,
            I.prototype.wordwrap = function(t, e) {
                if (!t)
                    return t;
                var i = "(.{1," + (e = e || 64) + "})( +|$\n?)|(.{1," + e + "})";
                return t.match(RegExp(i, "g")).join("\n")
            }
            ,
            I.prototype.getPrivateKey = function() {
                var t = "-----BEGIN RSA PRIVATE KEY-----\n";
                return t += this.wordwrap(this.getPrivateBaseKeyB64()) + "\n",
                t += "-----END RSA PRIVATE KEY-----"
            }
            ,
            I.prototype.getPublicKey = function() {
                var t = "-----BEGIN PUBLIC KEY-----\n";
                return t += this.wordwrap(this.getPublicBaseKeyB64()) + "\n",
                t += "-----END PUBLIC KEY-----"
            }
            ,
            I.prototype.hasPublicKeyProperty = function(t) {
                return (t = t || {}).hasOwnProperty("n") && t.hasOwnProperty("e")
            }
            ,
            I.prototype.hasPrivateKeyProperty = function(t) {
                return (t = t || {}).hasOwnProperty("n") && t.hasOwnProperty("e") && t.hasOwnProperty("d") && t.hasOwnProperty("p") && t.hasOwnProperty("q") && t.hasOwnProperty("dmp1") && t.hasOwnProperty("dmq1") && t.hasOwnProperty("coeff")
            }
            ,
            I.prototype.parsePropertiesFrom = function(t) {
                this.n = t.n,
                this.e = t.e,
                t.hasOwnProperty("d") && (this.d = t.d,
                this.p = t.p,
                this.q = t.q,
                this.dmp1 = t.dmp1,
                this.dmq1 = t.dmq1,
                this.coeff = t.coeff)
            }
            ;
            var k = function(t) {
                I.call(this),
                t && ("string" === typeof t ? this.parseKey(t) : (this.hasPrivateKeyProperty(t) || this.hasPublicKeyProperty(t)) && this.parsePropertiesFrom(t))
            };
            (k.prototype = new I).constructor = k;
            var F = function(t) {
                t = t || {},
                this.default_key_size = parseInt(t.default_key_size) || 1024,
                this.default_public_exponent = t.default_public_exponent || "010001",
                this.log = t.log || !1,
                this.key = null
            };
            F.prototype.setKey = function(t) {
                this.log && this.key && console.warn("A key was already set, overriding existing."),
                this.key = new k(t)
            }
            ,
            F.prototype.setPrivateKey = function(t) {
                this.setKey(t)
            }
            ,
            F.prototype.setPublicKey = function(t) {
                this.setKey(t)
            }
            ,
            F.prototype.decrypt = function(t) {
                try {
                    return this.getKey().decrypt(L(t))
                } catch (e) {
                    return !1
                }
            }
            ,
            F.prototype.encrypt = function(t) {
                try {
                    return M(this.getKey().encrypt(t))
                } catch (e) {
                    return !1
                }
            }
            ,
            F.prototype.getKey = function(t) {
                if (!this.key) {
                    if (this.key = new k,
                    t && "[object Function]" === {}.toString.call(t))
                        return void this.key.generateAsync(this.default_key_size, this.default_public_exponent, t);
                    this.key.generate(this.default_key_size, this.default_public_exponent)
                }
                return this.key
            }
            ,
            F.prototype.getPrivateKey = function() {
                return this.getKey().getPrivateKey()
            }
            ,
            F.prototype.getPrivateKeyB64 = function() {
                return this.getKey().getPrivateBaseKeyB64()
            }
            ,
            F.prototype.getPublicKey = function() {
                return this.getKey().getPublicKey()
            }
            ,
            F.prototype.getPublicKeyB64 = function() {
                return this.getKey().getPublicBaseKeyB64()
            }
            ,
            F.version = "2.3.1",
            t.JSEncrypt = F
        }
        ) ? r.apply(e, n) : r) || (t.exports = s)
    }
    }
)
// 加载模块363，该模块会设置enc函数
jzq(363)

// 确保enc函数已经被正确设置后再调用
    res = enc('18812138110')
    console.log(res)