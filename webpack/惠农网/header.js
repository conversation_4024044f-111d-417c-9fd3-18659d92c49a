const CryptoJS=require('crypto-js')
I={
    default:function(p){
        return CryptoJS.SHA256(p)
    }
}
c={
    default:function(p){
        return CryptoJS.SHA384(p)
    }
}
r={
    default:function(p){
        return CryptoJS.MD5(p)
    }
}
o={
    default:function(p){
        return CryptoJS.SHA1(p)
    }
}
Q={
    "nonce": "321ed16940d9da263a842c3373734505",
    "timestamp": "1754725399271",
    "deviceId": "5cd81d5-cf40-45bd-ad54-be65f4264",
    "secret": "!@iiD5R4Mljlk4JWYk*YOiub2RnKkahR",
    "secretType": 3
}

 var sign= (V = (h = Q).nonce,
            R = h.timestamp,
            E = h.deviceId,
            j = h.secret,
            D = h.secretType,
            v = V,
            F = l.default(v)['toString'](),
            m = R,
            A =  c.default(m)['toString'](),
            _ = E,
            w = V,
            $ = r.default(w + ('(lo__ol)') + _)['toString'](),
            S = j,
            M = R,
            W = "",
            1 === (O = D) ? W = (0,
            r.default)(M + S)['toString']() : 2 === O ? W = (0,
            o.default)(S + M)['toString']() : 3 === O && (W = (0,
            o.default)(M + ('_hnw_+_--_123_)') + S)['toString']()),
            W = W['substring'](W['length'] - 16, W["length"] - 1),
            T = d['fromString'](W, !0, 16)['toUnsigned']()['toString'](10),
            P = [F, A, $, T],
            I = 1 === (k = D) ? P["reduce"]((function(e, t) {
                return e + "" + t
            }
            )) : 2 === k ? P['reduce']((function(e, t) {
                return e + "!" + t
            }
            )) : 3 === k ? P.reduce((function(e, t) {
                return e + ('(o1o)') + t
            }
            )) : void 0,
            (0,
            c.default)(I)).toString()
