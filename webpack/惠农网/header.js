window=global
const CryptoJS=require('crypto-js')
l={
    default:function(p){
        return CryptoJS.SHA256(p)
    }
}
c={
    default:function(p){
        return CryptoJS.SHA384(p)
    }
}
r={
    default:function(p){
        return CryptoJS.MD5(p)
    }
}
o={
    default:function(p){
        return CryptoJS.SHA1(p)
    }
}
Q={
    "nonce": "321ed16940d9da263a842c3373734505",
    "timestamp": "1754725399271",
    "deviceId": "5cd81d5-cf40-45bd-ad54-be65f4264",
    "secret": "!@iiD5R4Mljlk4JWYk*YOiub2RnKkahR",
    "secretType": 3
}
!function(e) {
    function r(data) {
        for (var r, n, o = data[0], f = data[1], l = data[2], i = 0, v = []; i < o.length; i++)
            n = o[i],
            Object.prototype.hasOwnProperty.call(d, n) && d[n] && v.push(d[n][0]),
            d[n] = 0;
        for (r in f)
            Object.prototype.hasOwnProperty.call(f, r) && (e[r] = f[r]);
        for (h && h(data); v.length; )
            v.shift()();
        return c.push.apply(c, l || []),
        t()
    }
    function t() {
        for (var e, i = 0; i < c.length; i++) {
            for (var r = c[i], t = !0, n = 1; n < r.length; n++) {
                var o = r[n];
                0 !== d[o] && (t = !1)
            }
            t && (c.splice(i--, 1),
            e = f(f.s = r[0]))
        }
        return e
    }
    var n = {}
      , o = {
        11: 0
    }
      , d = {
        11: 0
    }
      , c = [];
    function f(r) {
        if (n[r])
            return n[r].exports;
        var t = n[r] = {
            i: r,
            l: !1,
            exports: {}
        };
        return e[r].call(t.exports, t, t.exports, f),
        t.l = !0,
        t.exports
    }
    f.e = function(e) {
        var r = [];
        o[e] ? r.push(o[e]) : 0 !== o[e] && {
            0: 1,
            3: 1,
            4: 1,
            5: 1
        }[e] && r.push(o[e] = new Promise((function(r, t) {
            for (var n = ({
                0: "index-vue/index",
                1: "vendors.index-vue/index.ssr/gongying/_id",
                3: "errorPages-errorPage404-vue/index",
                4: "errorPages-errorPage500-vue/index",
                5: "errorPages-shopShelves-vue/index",
                12: "ssr/gongying/_id"
            }[e] || e) + "." + {
                0: "c8b546768672a3eea42b",
                1: "31d6cfe0d16ae931b73c",
                3: "49c5c6724ec4bd2e8adf",
                4: "4e4e836b08bd4a58e376",
                5: "899dfdfa1fabd48b8dee",
                12: "31d6cfe0d16ae931b73c"
            }[e] + ".css", d = f.p + n, c = document.getElementsByTagName("link"), i = 0; i < c.length; i++) {
                var l = (h = c[i]).getAttribute("data-href") || h.getAttribute("href");
                if ("stylesheet" === h.rel && (l === n || l === d))
                    return r()
            }
            var v = document.getElementsByTagName("style");
            for (i = 0; i < v.length; i++) {
                var h;
                if ((l = (h = v[i]).getAttribute("data-href")) === n || l === d)
                    return r()
            }
            var m = document.createElement("link");
            m.rel = "stylesheet",
            m.type = "text/css",
            m.onload = r,
            m.onerror = function(r) {
                var n = r && r.target && r.target.src || d
                  , c = new Error("Loading CSS chunk " + e + " failed.\n(" + n + ")");
                c.code = "CSS_CHUNK_LOAD_FAILED",
                c.request = n,
                delete o[e],
                m.parentNode.removeChild(m),
                t(c)
            }
            ,
            m.href = d,
            document.getElementsByTagName("head")[0].appendChild(m)
        }
        )).then((function() {
            o[e] = 0
        }
        )));
        var t = d[e];
        if (0 !== t)
            if (t)
                r.push(t[2]);
            else {
                var n = new Promise((function(r, n) {
                    t = d[e] = [r, n]
                }
                ));
                r.push(t[2] = n);
                var c, script = document.createElement("script");
                script.charset = "utf-8",
                script.timeout = 120,
                f.nc && script.setAttribute("nonce", f.nc),
                script.src = function(e) {
                    return f.p + "" + ({
                        0: "index-vue/index",
                        1: "vendors.index-vue/index.ssr/gongying/_id",
                        3: "errorPages-errorPage404-vue/index",
                        4: "errorPages-errorPage500-vue/index",
                        5: "errorPages-shopShelves-vue/index",
                        12: "ssr/gongying/_id"
                    }[e] || e) + "." + {
                        0: "f0dbdd8d7099e736e361",
                        1: "6c828182e937a3d945ac",
                        3: "cc27a8b6d34be4b8af14",
                        4: "e2d84cf87cd79582ee30",
                        5: "d519e07d807cef4fa330",
                        12: "020be94e38224aef68fb"
                    }[e] + ".js"
                }(e);
                var l = new Error;
                c = function(r) {
                    script.onerror = script.onload = null,
                    clearTimeout(v);
                    var t = d[e];
                    if (0 !== t) {
                        if (t) {
                            var n = r && ("load" === r.type ? "missing" : r.type)
                              , o = r && r.target && r.target.src;
                            l.message = "Loading chunk " + e + " failed.\n(" + n + ": " + o + ")",
                            l.name = "ChunkLoadError",
                            l.type = n,
                            l.request = o,
                            t[1](l)
                        }
                        d[e] = void 0
                    }
                }
                ;
                var v = setTimeout((function() {
                    c({
                        type: "timeout",
                        target: script
                    })
                }
                ), 12e4);
                script.onerror = script.onload = c,
                document.head.appendChild(script)
            }
        return Promise.all(r)
    }
    ,
    f.m = e,
    f.c = n,
    f.d = function(e, r, t) {
        f.o(e, r) || Object.defineProperty(e, r, {
            enumerable: !0,
            get: t
        })
    }
    ,
    f.r = function(e) {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
            value: "Module"
        }),
        Object.defineProperty(e, "__esModule", {
            value: !0
        })
    }
    ,
    f.t = function(e, r) {
        if (1 & r && (e = f(e)),
        8 & r)
            return e;
        if (4 & r && "object" == typeof e && e && e.__esModule)
            return e;
        var t = Object.create(null);
        if (f.r(t),
        Object.defineProperty(t, "default", {
            enumerable: !0,
            value: e
        }),
        2 & r && "string" != typeof e)
            for (var n in e)
                f.d(t, n, function(r) {
                    return e[r]
                }
                .bind(null, n));
        return t
    }
    ,
    f.n = function(e) {
        var r = e && e.__esModule ? function() {
            return e.default
        }
        : function() {
            return e
        }
        ;
        return f.d(r, "a", r),
        r
    }
    ,
    f.o = function(object, e) {
        return Object.prototype.hasOwnProperty.call(object, e)
    }
    ,
    f.p = "//files.cnhnb.com/master-ssr/supplydetails/",
    f.oe = function(e) {
        throw console.error(e),
        e
    }
    ;
    var l = window.webpackJsonp = window.webpackJsonp || []
      , v = l.push.bind(l);
    l.push = r,
    l = l.slice();
    for (var i = 0; i < l.length; i++)
        r(l[i]);
    var h = v;
    // t()
    jzq=f
}([
    function(){
        console.log(0);
    },
    function(t, e) {
    t.exports = r;
    var n = null;
    try {
        n = new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 13, 2, 96, 0, 1, 127, 96, 4, 127, 127, 127, 127, 1, 127, 3, 7, 6, 0, 1, 1, 1, 1, 1, 6, 6, 1, 127, 1, 65, 0, 11, 7, 50, 6, 3, 109, 117, 108, 0, 1, 5, 100, 105, 118, 95, 115, 0, 2, 5, 100, 105, 118, 95, 117, 0, 3, 5, 114, 101, 109, 95, 115, 0, 4, 5, 114, 101, 109, 95, 117, 0, 5, 8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0, 10, 191, 1, 6, 4, 0, 35, 0, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11])),{}).exports
    } catch (t) {}
    function r(t, e, n) {
        this.low = 0 | t,
        this.high = 0 | e,
        this.unsigned = !!n
    }
    function o(t) {
        return !0 === (t && t.__isLong__)
    }
    r.prototype.__isLong__,
    Object.defineProperty(r.prototype, "__isLong__", {
        value: !0
    }),
    r.isLong = o;
    var h = {}
      , l = {};
    function f(t, e) {
        var n, r, o;
        return e ? (o = 0 <= (t >>>= 0) && t < 256) && (r = l[t]) ? r : (n = d(t, (0 | t) < 0 ? -1 : 0, !0),
        o && (l[t] = n),
        n) : (o = -128 <= (t |= 0) && t < 128) && (r = h[t]) ? r : (n = d(t, t < 0 ? -1 : 0, !1),
        o && (h[t] = n),
        n)
    }
    function c(t, e) {
        if (isNaN(t))
            return e ? E : x;
        if (e) {
            if (t < 0)
                return E;
            if (t >= _)
                return O
        } else {
            if (t <= -M)
                return P;
            if (t + 1 >= M)
                return A
        }
        return t < 0 ? c(-t, e).neg() : d(t % w | 0, t / w | 0, e)
    }
    function d(t, e, n) {
        return new r(t,e,n)
    }
    r.fromInt = f,
    r.fromNumber = c,
    r.fromBits = d;
    var m = Math.pow;
    function v(t, e, n) {
        if (0 === t.length)
            throw Error("empty string");
        if ("NaN" === t || "Infinity" === t || "+Infinity" === t || "-Infinity" === t)
            return x;
        if ("number" == typeof e ? (n = e,
        e = !1) : e = !!e,
        (n = n || 10) < 2 || 36 < n)
            throw RangeError("radix");
        var p;
        if ((p = t.indexOf("-")) > 0)
            throw Error("interior hyphen");
        if (0 === p)
            return v(t.substring(1), e, n).neg();
        for (var r = c(m(n, 8)), o = x, i = 0; i < t.length; i += 8) {
            var h = Math.min(8, t.length - i)
              , l = parseInt(t.substring(i, i + h), n);
            if (h < 8) {
                var f = c(m(n, h));
                o = o.mul(f).add(c(l))
            } else
                o = (o = o.mul(r)).add(c(l))
        }
        return o.unsigned = e,
        o
    }
    function y(t, e) {
        return "number" == typeof t ? c(t, e) : "string" == typeof t ? v(t, e) : d(t.low, t.high, "boolean" == typeof e ? e : t.unsigned)
    }
    r.fromString = v,
    r.fromValue = y;
    var w = 4294967296
      , _ = w * w
      , M = _ / 2
      , S = f(1 << 24)
      , x = f(0);
    r.ZERO = x;
    var E = f(0, !0);
    r.UZERO = E;
    var k = f(1);
    r.ONE = k;
    var C = f(1, !0);
    r.UONE = C;
    var T = f(-1);
    r.NEG_ONE = T;
    var A = d(-1, 2147483647, !1);
    r.MAX_VALUE = A;
    var O = d(-1, -1, !0);
    r.MAX_UNSIGNED_VALUE = O;
    var P = d(0, -2147483648, !1);
    r.MIN_VALUE = P;
    var R = r.prototype;
    R.toInt = function() {
        return this.unsigned ? this.low >>> 0 : this.low
    }
    ,
    R.toNumber = function() {
        return this.unsigned ? (this.high >>> 0) * w + (this.low >>> 0) : this.high * w + (this.low >>> 0)
    }
    ,
    R.toString = function(t) {
        if ((t = t || 10) < 2 || 36 < t)
            throw RangeError("radix");
        if (this.isZero())
            return "0";
        if (this.isNegative()) {
            if (this.eq(P)) {
                var e = c(t)
                  , div = this.div(e)
                  , n = div.mul(e).sub(this);
                return div.toString(t) + n.toInt().toString(t)
            }
            return "-" + this.neg().toString(t)
        }
        for (var r = c(m(t, 6), this.unsigned), o = this, h = ""; ; ) {
            var l = o.div(r)
              , f = (o.sub(l.mul(r)).toInt() >>> 0).toString(t);
            if ((o = l).isZero())
                return f + h;
            for (; f.length < 6; )
                f = "0" + f;
            h = "" + f + h
        }
    }
    ,
    R.getHighBits = function() {
        return this.high
    }
    ,
    R.getHighBitsUnsigned = function() {
        return this.high >>> 0
    }
    ,
    R.getLowBits = function() {
        return this.low
    }
    ,
    R.getLowBitsUnsigned = function() {
        return this.low >>> 0
    }
    ,
    R.getNumBitsAbs = function() {
        if (this.isNegative())
            return this.eq(P) ? 64 : this.neg().getNumBitsAbs();
        for (var t = 0 != this.high ? this.high : this.low, e = 31; e > 0 && 0 == (t & 1 << e); e--)
            ;
        return 0 != this.high ? e + 33 : e + 1
    }
    ,
    R.isZero = function() {
        return 0 === this.high && 0 === this.low
    }
    ,
    R.eqz = R.isZero,
    R.isNegative = function() {
        return !this.unsigned && this.high < 0
    }
    ,
    R.isPositive = function() {
        return this.unsigned || this.high >= 0
    }
    ,
    R.isOdd = function() {
        return 1 == (1 & this.low)
    }
    ,
    R.isEven = function() {
        return 0 == (1 & this.low)
    }
    ,
    R.equals = function(t) {
        return o(t) || (t = y(t)),
        (this.unsigned === t.unsigned || this.high >>> 31 != 1 || t.high >>> 31 != 1) && (this.high === t.high && this.low === t.low)
    }
    ,
    R.eq = R.equals,
    R.notEquals = function(t) {
        return !this.eq(t)
    }
    ,
    R.neq = R.notEquals,
    R.ne = R.notEquals,
    R.lessThan = function(t) {
        return this.comp(t) < 0
    }
    ,
    R.lt = R.lessThan,
    R.lessThanOrEqual = function(t) {
        return this.comp(t) <= 0
    }
    ,
    R.lte = R.lessThanOrEqual,
    R.le = R.lessThanOrEqual,
    R.greaterThan = function(t) {
        return this.comp(t) > 0
    }
    ,
    R.gt = R.greaterThan,
    R.greaterThanOrEqual = function(t) {
        return this.comp(t) >= 0
    }
    ,
    R.gte = R.greaterThanOrEqual,
    R.ge = R.greaterThanOrEqual,
    R.compare = function(t) {
        if (o(t) || (t = y(t)),
        this.eq(t))
            return 0;
        var e = this.isNegative()
          , n = t.isNegative();
        return e && !n ? -1 : !e && n ? 1 : this.unsigned ? t.high >>> 0 > this.high >>> 0 || t.high === this.high && t.low >>> 0 > this.low >>> 0 ? -1 : 1 : this.sub(t).isNegative() ? -1 : 1
    }
    ,
    R.comp = R.compare,
    R.negate = function() {
        return !this.unsigned && this.eq(P) ? P : this.not().add(k)
    }
    ,
    R.neg = R.negate,
    R.add = function(t) {
        o(t) || (t = y(t));
        var e = this.high >>> 16
          , n = 65535 & this.high
          , r = this.low >>> 16
          , h = 65535 & this.low
          , l = t.high >>> 16
          , f = 65535 & t.high
          , c = t.low >>> 16
          , m = 0
          , v = 0
          , w = 0
          , _ = 0;
        return w += (_ += h + (65535 & t.low)) >>> 16,
        v += (w += r + c) >>> 16,
        m += (v += n + f) >>> 16,
        m += e + l,
        d((w &= 65535) << 16 | (_ &= 65535), (m &= 65535) << 16 | (v &= 65535), this.unsigned)
    }
    ,
    R.subtract = function(t) {
        return o(t) || (t = y(t)),
        this.add(t.neg())
    }
    ,
    R.sub = R.subtract,
    R.multiply = function(t) {
        if (this.isZero())
            return x;
        if (o(t) || (t = y(t)),
        n)
            return d(n.mul(this.low, this.high, t.low, t.high), n.get_high(), this.unsigned);
        if (t.isZero())
            return x;
        if (this.eq(P))
            return t.isOdd() ? P : x;
        if (t.eq(P))
            return this.isOdd() ? P : x;
        if (this.isNegative())
            return t.isNegative() ? this.neg().mul(t.neg()) : this.neg().mul(t).neg();
        if (t.isNegative())
            return this.mul(t.neg()).neg();
        if (this.lt(S) && t.lt(S))
            return c(this.toNumber() * t.toNumber(), this.unsigned);
        var e = this.high >>> 16
          , r = 65535 & this.high
          , h = this.low >>> 16
          , l = 65535 & this.low
          , f = t.high >>> 16
          , m = 65535 & t.high
          , v = t.low >>> 16
          , w = 65535 & t.low
          , _ = 0
          , M = 0
          , E = 0
          , k = 0;
        return E += (k += l * w) >>> 16,
        M += (E += h * w) >>> 16,
        E &= 65535,
        M += (E += l * v) >>> 16,
        _ += (M += r * w) >>> 16,
        M &= 65535,
        _ += (M += h * v) >>> 16,
        M &= 65535,
        _ += (M += l * m) >>> 16,
        _ += e * w + r * v + h * m + l * f,
        d((E &= 65535) << 16 | (k &= 65535), (_ &= 65535) << 16 | (M &= 65535), this.unsigned)
    }
    ,
    R.mul = R.multiply,
    R.divide = function(t) {
        if (o(t) || (t = y(t)),
        t.isZero())
            throw Error("division by zero");
        var e, r, h;
        if (n)
            return this.unsigned || -2147483648 !== this.high || -1 !== t.low || -1 !== t.high ? d((this.unsigned ? n.div_u : n.div_s)(this.low, this.high, t.low, t.high), n.get_high(), this.unsigned) : this;
        if (this.isZero())
            return this.unsigned ? E : x;
        if (this.unsigned) {
            if (t.unsigned || (t = t.toUnsigned()),
            t.gt(this))
                return E;
            if (t.gt(this.shru(1)))
                return C;
            h = E
        } else {
            if (this.eq(P))
                return t.eq(k) || t.eq(T) ? P : t.eq(P) ? k : (e = this.shr(1).div(t).shl(1)).eq(x) ? t.isNegative() ? k : T : (r = this.sub(t.mul(e)),
                h = e.add(r.div(t)));
            if (t.eq(P))
                return this.unsigned ? E : x;
            if (this.isNegative())
                return t.isNegative() ? this.neg().div(t.neg()) : this.neg().div(t).neg();
            if (t.isNegative())
                return this.div(t.neg()).neg();
            h = x
        }
        for (r = this; r.gte(t); ) {
            e = Math.max(1, Math.floor(r.toNumber() / t.toNumber()));
            for (var l = Math.ceil(Math.log(e) / Math.LN2), f = l <= 48 ? 1 : m(2, l - 48), v = c(e), w = v.mul(t); w.isNegative() || w.gt(r); )
                w = (v = c(e -= f, this.unsigned)).mul(t);
            v.isZero() && (v = k),
            h = h.add(v),
            r = r.sub(w)
        }
        return h
    }
    ,
    R.div = R.divide,
    R.modulo = function(t) {
        return o(t) || (t = y(t)),
        n ? d((this.unsigned ? n.rem_u : n.rem_s)(this.low, this.high, t.low, t.high), n.get_high(), this.unsigned) : this.sub(this.div(t).mul(t))
    }
    ,
    R.mod = R.modulo,
    R.rem = R.modulo,
    R.not = function() {
        return d(~this.low, ~this.high, this.unsigned)
    }
    ,
    R.and = function(t) {
        return o(t) || (t = y(t)),
        d(this.low & t.low, this.high & t.high, this.unsigned)
    }
    ,
    R.or = function(t) {
        return o(t) || (t = y(t)),
        d(this.low | t.low, this.high | t.high, this.unsigned)
    }
    ,
    R.xor = function(t) {
        return o(t) || (t = y(t)),
        d(this.low ^ t.low, this.high ^ t.high, this.unsigned)
    }
    ,
    R.shiftLeft = function(t) {
        return o(t) && (t = t.toInt()),
        0 == (t &= 63) ? this : t < 32 ? d(this.low << t, this.high << t | this.low >>> 32 - t, this.unsigned) : d(0, this.low << t - 32, this.unsigned)
    }
    ,
    R.shl = R.shiftLeft,
    R.shiftRight = function(t) {
        return o(t) && (t = t.toInt()),
        0 == (t &= 63) ? this : t < 32 ? d(this.low >>> t | this.high << 32 - t, this.high >> t, this.unsigned) : d(this.high >> t - 32, this.high >= 0 ? 0 : -1, this.unsigned)
    }
    ,
    R.shr = R.shiftRight,
    R.shiftRightUnsigned = function(t) {
        if (o(t) && (t = t.toInt()),
        0 === (t &= 63))
            return this;
        var e = this.high;
        return t < 32 ? d(this.low >>> t | e << 32 - t, e >>> t, this.unsigned) : d(32 === t ? e : e >>> t - 32, 0, this.unsigned)
    }
    ,
    R.shru = R.shiftRightUnsigned,
    R.shr_u = R.shiftRightUnsigned,
    R.toSigned = function() {
        return this.unsigned ? d(this.low, this.high, !1) : this
    }
    ,
    R.toUnsigned = function() {
        return this.unsigned ? this : d(this.low, this.high, !0)
    }
    ,
    R.toBytes = function(t) {
        return t ? this.toBytesLE() : this.toBytesBE()
    }
    ,
    R.toBytesLE = function() {
        var t = this.high
          , e = this.low;
        return [255 & e, e >>> 8 & 255, e >>> 16 & 255, e >>> 24, 255 & t, t >>> 8 & 255, t >>> 16 & 255, t >>> 24]
    }
    ,
    R.toBytesBE = function() {
        var t = this.high
          , e = this.low;
        return [t >>> 24, t >>> 16 & 255, t >>> 8 & 255, 255 & t, e >>> 24, e >>> 16 & 255, e >>> 8 & 255, 255 & e]
    }
    ,
    r.fromBytes = function(t, e, n) {
        return n ? r.fromBytesLE(t, e) : r.fromBytesBE(t, e)
    }
    ,
    r.fromBytesLE = function(t, e) {
        return new r(t[0] | t[1] << 8 | t[2] << 16 | t[3] << 24,t[4] | t[5] << 8 | t[6] << 16 | t[7] << 24,e)
    }
    ,
    r.fromBytesBE = function(t, e) {
        return new r(t[4] << 24 | t[5] << 16 | t[6] << 8 | t[7],t[0] << 24 | t[1] << 16 | t[2] << 8 | t[3],e)
    }
}
]);
 jzq=(0)
 var sign= (V = (h = Q).nonce,
            R = h.timestamp,
            E = h.deviceId,
            j = h.secret,
            D = h.secretType,
            v = V,
            F = l.default(v)['toString'](),
            m = R,
            A =  c.default(m)['toString'](),
            _ = E,
            w = V,
            $ = r.default(w + ('(lo__ol)') + _)['toString'](),
            S = j,
            M = R,
            W = "",
            1 === (O = D) ? W = (0,
            r.default)(M + S)['toString']() : 2 === O ? W = (0,
            o.default)(S + M)['toString']() : 3 === O && (W = (0,
            o.default)(M + ('_hnw_+_--_123_)') + S)['toString']()),
            W = W['substring'](W['length'] - 16, W["length"] - 1),
            // T = d['fromString'](W, !0, 16)["toUnsigned"]()["toString"](10),

            T = '276338461686661103',
            P = [F, A, $, T],
            I = 1 === (k = D) ? P["reduce"]((function(e, t) {
                return e + "" + t
            }
            )) : 2 === k ? P['reduce']((function(e, t) {
                return e + "!" + t
            }
            )) : 3 === k ? P.reduce((function(e, t) {
                return e + ('(o1o)') + t
            }
            )) : void 0,
            (0,
            c.default)(I)).toString()
            console.log(sign);